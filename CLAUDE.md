# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

PyRT-DICOM is a Python library for creating radiotherapy DICOM files with strongly-typed, IntelliSense-friendly interfaces. The project is currently in **Proof of Concept (POC) phase** focusing on RT Dose IOD to validate the modular approach using multiple inheritance.

## Core Architecture

### Design Philosophy
- **Modular Architecture**: IODs composed from individual DICOM modules using multiple inheritance
- **Type Safety**: All DICOM data elements as class attributes for IntelliSense support
- **No Free Text Configuration**: Strongly-typed interfaces with strict enum enforcement
- **Optional Validation**: No validation at creation, validate on-demand or during export
- **Auto-Generated UIDs**: UIDs generated at object creation with cross-dataset linking
- **Memory Storage**: All data stored in memory for fast access and simplicity

### Key Architectural Decisions
- **Composition over Inheritance**: IODs use module composition, not multiple inheritance
- **Simple Inheritance**: IODs inherit only from SimpleBaseIOD (which inherits from FileDataset)
- **Property Delegation**: IntelliSense benefits maintained through strategic property delegation
- **Progressive Development**: Start with POC, validate approach, then scale
- **Test-driven Development**: Pytest framework with comprehensive coverage

### Project Structure
```
src/pyrt_dicom/
├── __init__.py                 # Package initialization
├── base/                       # Base classes (added after POC)
│   └── base_iod.py            # BaseIOD with dynamic introspection
├── modules/                    # DICOM module implementations
│   ├── patient_module.py      # Patient Module (C.7.1.1)
│   └── ...                    # Additional modules (future)
├── iods/                      # Information Object Definitions
│   ├── rt_dose_iod.py         # RT Dose IOD (A.18.3)
│   └── ...                    # Additional IODs (future)
├── enums/                     # Strongly-typed DICOM enumerations
│   ├── patient_enums.py       # Patient-related enums
│   ├── dose_enums.py          # Dose-related enums
│   └── ...                    # Additional enums
└── validators/                # Validation framework
    ├── base_validator.py      # Base validation functionality
    ├── patient_validator.py   # Patient Module validator
    └── ...                    # Additional validators
```

## Development Commands

### Environment Setup
```bash
# Install in development mode
pip install -e .

# Install with test dependencies
pip install -e .[test]

# Install with type checking
pip install -e . && pip install mypy
```

### Testing
```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=src/pyrt_dicom --cov-report=html

# Run specific test file
pytest tests/modules/test_patient_module.py -v

# Run specific test
pytest tests/modules/test_patient_module.py::TestPatientModule::test_required_fields -v
```

### Type Checking
```bash
# Run mypy type checking
mypy src/pyrt_dicom tests

# Type check with Hatch environment
hatch run types:check
```

### Code Quality
```bash
# Format code (if configured)
black src/ tests/

# Lint code (if configured) 
flake8 src/ tests/

# Sort imports (if configured)
isort src/ tests/
```

## Current Implementation Status

### Completed (POC Phase)
- ✅ **PatientModule**: Complete DICOM PS3.3 C.7.1.1 implementation with all conditional requirements
- ✅ **RTDose IOD**: Basic implementation with PatientModule integration
- ✅ **Validation Framework**: PatientValidator with comprehensive validation
- ✅ **Enums**: PatientSex, DoseUnits, DoseType, and related enumerations
- ✅ **BaseIOD**: Dynamic introspection for module composition (added post-POC)

### In Progress
- 🔄 **Testing**: Comprehensive test coverage for existing modules
- 🔄 **Documentation**: API documentation and usage examples

### Future Phases
- ⏳ **Additional Modules**: GeneralStudyModule, RTSeriesModule, etc.
- ⏳ **Complete IODs**: RTPlan, RTStruct, CTImage, RTImage
- ⏳ **Advanced Features**: Cross-reference validation, DICOM compliance checker

## Implementation Patterns

### Creating New Modules (unchanged - this works well)
```python
class NewModule(Dataset):
    """Module description - DICOM PS3.3 reference.
    
    Usage:
        module = NewModule.from_required_elements(
            required_param="value"
        ).with_optional_elements(
            optional_param="value"
        )
    """
    
    @classmethod
    def from_required_elements(cls, *args, **kwargs) -> 'NewModule':
        """Create module with all required (Type 1 [*args] and Type 2 [**kwargs]) elements."""
        instance = cls()
        # Set required elements
        return instance
    
    def with_optional_elements(self, **kwargs) -> 'NewModule':
        """Add optional (Type 3) elements with default value of None"""
        # Set optional elements
        return self
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data."""
        return ModuleValidator.validate(self, config)
```

### Creating New IODs (simplified approach)
```python
class NewIOD(SimpleBaseIOD):
    """IOD description - DICOM PS3.3 reference."""
    
    SOP_CLASS_UID = "1.2.840.10008.5.1.4.x.x.x"
    
    def __init__(self):
        """Initialize IOD with module composition."""
        super().__init__()
        self.patient_module: Optional[PatientModule] = None
        self.required_module: Optional[RequiredModule] = None
    
    @classmethod
    def from_required_modules(cls, patient_module: PatientModule, **modules) -> 'NewIOD':
        """Create IOD from required modules using composition."""
        instance = cls()
        
        # Add modules using composition (not inheritance)
        instance.add_module("patient", patient_module)
        instance.patient_module = patient_module
        
        for name, module in modules.items():
            instance.add_module(name, module)
            setattr(instance, f"{name}_module", module)
        
        return instance
```

## Important Development Guidelines

### POC Constraints
- **NO REFACTORING** until POC success criteria are met
- Focus on RT Dose IOD functionality and validation
- Maintain multiple inheritance approach for IntelliSense support
- Validate user experience with strongly-typed interfaces

### Testing Requirements
- All new modules must have comprehensive pytest tests
- Test both successful operations and validation failures
- Include DICOM compliance validation tests
- Use real-world usage scenarios in integration tests

### Code Quality Standards
- Google-style docstrings for all classes and methods
- Complete type hints for all function signatures
- Enum usage for all DICOM coded values (no free text)
- Validation methods return structured results (not exceptions)

### DICOM Compliance
- Follow DICOM PS3.3 specifications exactly
- Implement Type 1, 1C, 2, 2C, and 3 requirements correctly
- Use proper DICOM data element tags and VRs
- Generate valid DICOM files that pass external validation tools

## Dependencies

### Core Dependencies
- `pydicom>=2.4.0`: Core DICOM functionality
- `python>=3.11`: Modern Python features and type hints

### Development Dependencies  
- `pytest>=7.0.0`: Testing framework
- `pytest-cov>=4.0.0`: Coverage reporting
- `mypy>=1.0.0`: Static type checking (optional)

## Build System

The project uses **Hatch** as the build system with the following key configurations:
- Package metadata in `pyproject.toml`
- Version management from `src/pyrt_dicom/__about__.py`
- Test environment with coverage reporting
- Type checking environment with mypy

## Success Criteria for POC

Before any architectural changes or base class abstractions:
1. ✅ Create valid RT Dose DICOM files
2. ⏳ Positive stakeholder feedback on API design
3. ⏳ All POC tests passing with >90% coverage
4. ⏳ Complete documentation for POC functionality
5. ⏳ Validation confirms DICOM compliance

## Architecture Benefits

The simplified composition-based approach provides significant advantages:

**✅ Simpler Architecture**:
- IODs inherit only from SimpleBaseIOD → FileDataset
- No complex multiple inheritance or diamond problems
- Clear, linear inheritance hierarchy

**✅ Better Separation of Concerns**:
- Modules handle their own data and validation
- IODs orchestrate modules through composition
- Loose coupling allows independent module development

**✅ Maintains IntelliSense Benefits**:
- Property delegation provides direct access (e.g., `rt_dose.patients_name`)
- Type hints work perfectly with composition
- Method chaining still supported

**✅ Proper DICOM File Handling**:
- FileDataset inheritance provides correct file meta information
- Native DICOM file I/O capabilities
- Standards-compliant DICOM file generation

**✅ Easier Testing and Maintenance**:
- Modules can be tested independently
- IODs can be tested with mock modules
- Clear dependencies and relationships

**⚠️ CRITICAL: VALIDATE SIMPLIFIED APPROACH BEFORE ADDITIONAL FEATURES ⚠️**