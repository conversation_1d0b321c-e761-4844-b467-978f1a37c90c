"""
General Image Module - DICOM PS3.3 C.7.6.1

The General Image Module contains attributes that identify and describe 
an image within a particular Series.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod, ImageType
)
from ..validators.general_image_validator import GeneralImageValidator
from ..validators.base_validator import ValidationConfig


class GeneralImageModule(BaseModule):
    """General Image Module implementation for DICOM PS3.3 C.7.6.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that identify and describe an image within a particular Series.
    
    Usage:
        # Create with required elements
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation="A\\F"
        )
        
        # Add optional elements
        image.with_optional_elements(
            image_type=["ORIGINAL", "PRIMARY"],
            quality_control_image=QualityControlImage.NO,
            burned_in_annotation=BurnedInAnnotation.NO
        )
        
        # Add temporal elements if part of temporally related series
        image.with_temporal_elements(
            content_date="20240101",
            content_time="120000"
        )
        
        # Validate
        result = image.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        instance_number: str = "",
        patient_orientation: str = ""
    ) -> 'GeneralImageModule':
        """Create GeneralImageModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            instance_number (str): Number that identifies this image (0020,0013) Type 2
            patient_orientation (str): Patient direction of rows and columns (0020,0020) Type 2C.
                Format: "row_direction\\column_direction" (e.g., "A\\F" for anterior-foot)
            
        Returns:
            GeneralImageModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.InstanceNumber = instance_number
        instance.PatientOrientation = patient_orientation
        return instance
    
    def with_optional_elements(
        self,
        image_type: list[str] | None = None,
        image_comments: str | None = None,
        quality_control_image: str | QualityControlImage | None = None,
        burned_in_annotation: str | BurnedInAnnotation | None = None,
        recognizable_visual_features: str | RecognizableVisualFeatures | None = None,
        lossy_image_compression: str | LossyImageCompression | None = None,
        lossy_image_compression_ratio: list[float] | None = None,
        lossy_image_compression_method: list[str] | list[LossyImageCompressionMethod] | None = None,
        icon_image_sequence: list[dict[str, any]] | None = None,
        presentation_lut_shape: str | PresentationLUTShape | None = None,
        real_world_value_mapping_sequence: list[dict[str, any]] | None = None,
        image_laterality: str | ImageLaterality | None = None
    ) -> 'GeneralImageModule':
        """Add optional (Type 3) data elements.
        
        Args:
            image_type (list[str] | None): Image identification characteristics (0008,0008) Type 3.
                Multi-valued: [pixel_data_char, patient_exam_char, modality_specific, ...]
            image_comments (str | None): User-defined comments about the image (0020,4000) Type 3
            quality_control_image (str | QualityControlImage | None): Quality control material presence (0028,0300) Type 3
            burned_in_annotation (str | BurnedInAnnotation | None): Burned in annotation presence (0028,0301) Type 3
            recognizable_visual_features (str | RecognizableVisualFeatures | None): Visual features presence (0028,0302) Type 3
            lossy_image_compression (str | LossyImageCompression | None): Lossy compression flag (0028,2110) Type 3
            lossy_image_compression_ratio (list[float] | None): Compression ratio values (0028,2112) Type 3
            lossy_image_compression_method (list[str] | list[LossyImageCompressionMethod] | None): Compression methods (0028,2114) Type 3
            icon_image_sequence (list[dict[str, any]] | None): Representative icon image (0088,0200) Type 3
            presentation_lut_shape (str | PresentationLUTShape | None): Presentation LUT transformation (2050,0020) Type 3
            real_world_value_mapping_sequence (list[dict[str, any]] | None): Real world value mapping (0040,9096) Type 3
            image_laterality (str | ImageLaterality | None): Laterality of body part (0020,0062) Type 3
            
        Returns:
            GeneralImageModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('ImageType', image_type)
        self._set_attribute_if_not_none('ImageComments', image_comments)
        if quality_control_image is not None:
            self.QualityControlImage = self._format_enum_value(quality_control_image)
        if burned_in_annotation is not None:
            self.BurnedInAnnotation = self._format_enum_value(burned_in_annotation)
        if recognizable_visual_features is not None:
            self.RecognizableVisualFeatures = self._format_enum_value(recognizable_visual_features)
        if lossy_image_compression is not None:
            self.LossyImageCompression = self._format_enum_value(lossy_image_compression)
        self._set_attribute_if_not_none('LossyImageCompressionRatio', lossy_image_compression_ratio)
        if lossy_image_compression_method is not None:
            if isinstance(lossy_image_compression_method, list):
                self.LossyImageCompressionMethod = [self._format_enum_value(method) for method in lossy_image_compression_method]
            else:
                self.LossyImageCompressionMethod = self._format_enum_value(lossy_image_compression_method)
        self._set_attribute_if_not_none('IconImageSequence', icon_image_sequence)
        if presentation_lut_shape is not None:
            self.PresentationLUTShape = self._format_enum_value(presentation_lut_shape)
        self._set_attribute_if_not_none('RealWorldValueMappingSequence', real_world_value_mapping_sequence)
        if image_laterality is not None:
            self.ImageLaterality = self._format_enum_value(image_laterality)
        return self
    
    def with_temporal_elements(
        self,
        content_date: str | datetime | date | None = None,
        content_time: str | datetime | None = None
    ) -> 'GeneralImageModule':
        """Add temporal elements for temporally related series.
        
        Note: Content Date and Content Time are Type 2C - required if image is part 
        of a Series in which the images are temporally related.
        
        Args:
            content_date (str | datetime | date | None): Date image pixel data creation started (0008,0023) Type 2C
            content_time (str | datetime | None): Time image pixel data creation started (0008,0033) Type 2C
            
        Returns:
            GeneralImageModule: Self with temporal elements added
        """
        if content_date is not None:
            self.ContentDate = self._format_date_value(content_date)
        if content_time is not None:
            self.ContentTime = self._format_time_value(content_time)
        return self
    
    @staticmethod
    def create_icon_image_item(
        rows: int,
        columns: int,
        samples_per_pixel: int = 1,
        photometric_interpretation: str = "MONOCHROME2",
        bits_allocated: int = 8,
        bits_stored: int = 8,
        high_bit: int = 7,
        pixel_representation: int = 0,
        pixel_data: bytes | None = None
    ) -> dict[str, any]:
        """Create icon image sequence item.
        
        Args:
            rows (int): Number of rows in icon
            columns (int): Number of columns in icon
            samples_per_pixel (int): Number of samples per pixel (default: 1)
            photometric_interpretation (str): Photometric interpretation (default: "MONOCHROME2")
            bits_allocated (int): Bits allocated per sample (default: 8)
            bits_stored (int): Bits stored per sample (default: 8)
            high_bit (int): High bit position (default: 7)
            pixel_representation (int): Pixel representation (default: 0)
            pixel_data (bytes | None): Icon pixel data
            
        Returns:
            dict[str, any]: Icon image sequence item
        """
        item = {
            'Rows': rows,
            'Columns': columns,
            'SamplesPerPixel': samples_per_pixel,
            'PhotometricInterpretation': photometric_interpretation,
            'BitsAllocated': bits_allocated,
            'BitsStored': bits_stored,
            'HighBit': high_bit,
            'PixelRepresentation': pixel_representation
        }
        if pixel_data is not None:
            item['PixelData'] = pixel_data
        return item
    
    @property
    def is_quality_control(self) -> bool:
        """Check if this is a quality control image."""
        return hasattr(self, 'QualityControlImage') and self.QualityControlImage in ["YES", "BOTH"]
    
    @property
    def has_burned_in_annotation(self) -> bool:
        """Check if image has burned in annotation."""
        return hasattr(self, 'BurnedInAnnotation') and self.BurnedInAnnotation == "YES"
    
    @property
    def has_recognizable_features(self) -> bool:
        """Check if image has recognizable visual features."""
        return hasattr(self, 'RecognizableVisualFeatures') and self.RecognizableVisualFeatures == "YES"
    
    @property
    def is_lossy_compressed(self) -> bool:
        """Check if image has undergone lossy compression."""
        return hasattr(self, 'LossyImageCompression') and self.LossyImageCompression == "01"
    
    @property
    def has_temporal_info(self) -> bool:
        """Check if temporal information is present."""
        return hasattr(self, 'ContentDate') or hasattr(self, 'ContentTime')
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return GeneralImageValidator.validate(self, config)
