"""
RT Brachy Application Setups Module - DICOM PS3.3 C.8.8.15

The RT Brachy Application Setups Module contains information defining 
brachytherapy application setups for treatment delivery.

Note: This is a simplified implementation focusing on core functionality.
The full DICOM specification includes many additional sequences and attributes.
"""
from .base_module import BaseModule
from ..validators.rt_brachy_application_setups_validator import RTBrachyApplicationSetupsValidator
from ..validators.base_validator import ValidationConfig


class RTBrachyApplicationSetupsModule(BaseModule):
    """RT Brachy Application Setups Module implementation for DICOM PS3.3 C.8.8.15.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains information defining brachytherapy application setups.
    
    Note: This is a simplified implementation focusing on core functionality.
    
    Usage:
        # Create with required elements
        brachy_setups = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_application_setup_sequence=[
                brachy_setups.create_brachy_application_setup_item(
                    brachy_application_setup_number=1,
                    brachy_application_setup_name="Prostate Implant",
                    brachy_application_setup_type="PERMANENT"
                )
            ]
        )
        
        # Validate
        result = brachy_setups.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        brachy_treatment_technique: str,
        brachy_treatment_type: str,
        treatment_machine_sequence: list[dict[str, any]],
        source_sequence: list[dict[str, any]],
        application_setup_sequence: list[dict[str, any]]
    ) -> 'RTBrachyApplicationSetupsModule':
        """Create RTBrachyApplicationSetupsModule from all required (Type 1) data elements.

        Args:
            brachy_treatment_technique (str): Type of brachytherapy treatment technique (300A,0200) Type 1
            brachy_treatment_type (str): Type of brachytherapy treatment (300A,0202) Type 1
            treatment_machine_sequence (list[dict[str, any]]): Treatment machine information (300A,0206) Type 1
            source_sequence (list[dict[str, any]]): Sequence of sources (300A,0210) Type 1
            application_setup_sequence (list[dict[str, any]]): Application setups (300A,0230) Type 1

        Returns:
            RTBrachyApplicationSetupsModule: New module instance with required data elements set
        """
        instance = cls()
        instance.BrachyTreatmentTechnique = brachy_treatment_technique
        instance.BrachyTreatmentType = brachy_treatment_type
        instance.TreatmentMachineSequence = treatment_machine_sequence
        instance.SourceSequence = source_sequence
        instance.ApplicationSetupSequence = application_setup_sequence
        return instance
    
    def with_optional_elements(
        self
    ) -> 'RTBrachyApplicationSetupsModule':
        """Add optional (Type 3) elements.
        
        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the Brachy Application Setup Sequence.
        
        Returns:
            RTBrachyApplicationSetupsModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_brachy_application_setup_item(
        brachy_application_setup_number: int,
        brachy_application_setup_name: str = "",
        brachy_application_setup_type: str = "",
        brachy_application_setup_description: str | None = None,
        treatment_machine_name: str | None = None,
        source_sequence: list[dict[str, any]] | None = None,
        application_setup_sequence: list[dict[str, any]] | None = None,
        referenced_patient_setup_number: int | None = None
    ) -> dict[str, any]:
        """Create brachy application setup sequence item.
        
        Args:
            brachy_application_setup_number (int): Setup identification number (300A,0234) Type 1
            brachy_application_setup_name (str): Name of the setup (300A,0236) Type 2
            brachy_application_setup_type (str): Type of brachytherapy setup (300A,0238) Type 2
            brachy_application_setup_description (str | None): Description of setup (300A,023A) Type 3
            treatment_machine_name (str | None): Treatment machine name (300A,00B2) Type 3
            source_sequence (list[dict[str, any]] | None): Radioactive sources (300A,0210) Type 3
            application_setup_sequence (list[dict[str, any]] | None): Application setup details (300A,0240) Type 3
            referenced_patient_setup_number (int | None): Patient setup reference (300C,006A) Type 3
            
        Returns:
            dict: Brachy application setup sequence item
        """
        item = {
            'BrachyApplicationSetupNumber': brachy_application_setup_number,
            'BrachyApplicationSetupName': brachy_application_setup_name,
            'BrachyApplicationSetupType': brachy_application_setup_type
        }
        
        # Add optional elements if provided
        if brachy_application_setup_description is not None:
            item['BrachyApplicationSetupDescription'] = brachy_application_setup_description
        if treatment_machine_name is not None:
            item['TreatmentMachineName'] = treatment_machine_name
        if source_sequence is not None:
            item['SourceSequence'] = source_sequence
        if application_setup_sequence is not None:
            item['ApplicationSetupSequence'] = application_setup_sequence
        if referenced_patient_setup_number is not None:
            item['ReferencedPatientSetupNumber'] = referenced_patient_setup_number
        
        return item
    
    @staticmethod
    def create_source_item(
        source_number: int,
        source_isotope_name: str,
        source_isotope_half_life: float,
        reference_air_kerma_rate: float,
        source_encapsulation_nominal_thickness: float | None = None,
        source_encapsulation_nominal_transmission: float | None = None,
        source_isotope_nominal_activity: float | None = None,
        source_isotope_activity_reference_date: str | None = None,
        source_isotope_activity_reference_time: str | None = None
    ) -> dict[str, any]:
        """Create source sequence item.
        
        Args:
            source_number (int): Source identification number (300A,0212) Type 1
            source_isotope_name (str): Name of radioactive isotope (300A,0226) Type 1
            source_isotope_half_life (float): Half-life of isotope in days (300A,0228) Type 1
            reference_air_kerma_rate (float): Reference air kerma rate (300A,022A) Type 1
            source_encapsulation_nominal_thickness (float | None): Encapsulation thickness (300A,0222) Type 3
            source_encapsulation_nominal_transmission (float | None): Encapsulation transmission (300A,0224) Type 3
            source_isotope_nominal_activity (float | None): Nominal activity (300A,022C) Type 3
            source_isotope_activity_reference_date (str | None): Activity reference date (300A,022E) Type 3
            source_isotope_activity_reference_time (str | None): Activity reference time (300A,0230) Type 3
            
        Returns:
            dict: Source sequence item
        """
        item = {
            'SourceNumber': source_number,
            'SourceIsotopeName': source_isotope_name,
            'SourceIsotopeHalfLife': source_isotope_half_life,
            'ReferenceAirKermaRate': reference_air_kerma_rate
        }
        
        # Add optional elements if provided
        if source_encapsulation_nominal_thickness is not None:
            item['SourceEncapsulationNominalThickness'] = source_encapsulation_nominal_thickness
        if source_encapsulation_nominal_transmission is not None:
            item['SourceEncapsulationNominalTransmission'] = source_encapsulation_nominal_transmission
        if source_isotope_nominal_activity is not None:
            item['SourceIsotopeNominalActivity'] = source_isotope_nominal_activity
        if source_isotope_activity_reference_date is not None:
            item['SourceIsotopeActivityReferenceDate'] = source_isotope_activity_reference_date
        if source_isotope_activity_reference_time is not None:
            item['SourceIsotopeActivityReferenceTime'] = source_isotope_activity_reference_time
        
        return item
    
    @staticmethod
    def create_application_setup_item(
        application_setup_type: str,
        application_setup_number: int,
        application_setup_name: str | None = None,
        total_reference_air_kerma: float | None = None,
        brachy_accessory_device_sequence: list[dict[str, any]] | None = None,
        channel_sequence: list[dict[str, any]] | None = None
    ) -> dict[str, any]:
        """Create application setup sequence item.
        
        Args:
            application_setup_type (str): Type of application setup (300A,0242) Type 1
            application_setup_number (int): Setup identification number (300A,0244) Type 1
            application_setup_name (str | None): Name of setup (300A,0246) Type 3
            total_reference_air_kerma (float | None): Total reference air kerma (300A,0250) Type 3
            brachy_accessory_device_sequence (list[dict[str, any]] | None): Accessory devices (300A,0260) Type 3
            channel_sequence (list[dict[str, any]] | None): Treatment channels (300A,0280) Type 3
            
        Returns:
            dict: Application setup sequence item
        """
        item = {
            'ApplicationSetupType': application_setup_type,
            'ApplicationSetupNumber': application_setup_number
        }
        
        # Add optional elements if provided
        if application_setup_name is not None:
            item['ApplicationSetupName'] = application_setup_name
        if total_reference_air_kerma is not None:
            item['TotalReferenceAirKerma'] = total_reference_air_kerma
        if brachy_accessory_device_sequence is not None:
            item['BrachyAccessoryDeviceSequence'] = brachy_accessory_device_sequence
        if channel_sequence is not None:
            item['ChannelSequence'] = channel_sequence
        
        return item

    @staticmethod
    def create_treatment_machine_item(
        treatment_machine_name: str = "",
        manufacturer: str | None = None,
        institution_name: str | None = None,
        institution_address: str | None = None,
        institutional_department_name: str | None = None,
        institutional_department_type_code_sequence: list[dict[str, any]] | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        date_of_manufacture: str | None = None,
        date_of_installation: str | None = None
    ) -> dict[str, any]:
        """Create treatment machine sequence item.

        Args:
            treatment_machine_name (str): Name of treatment machine (300A,00B2) Type 2
            manufacturer (str | None): Equipment manufacturer (0008,0070) Type 3
            institution_name (str | None): Institution name (0008,0080) Type 3
            institution_address (str | None): Institution address (0008,0081) Type 3
            institutional_department_name (str | None): Department name (0008,1040) Type 3
            institutional_department_type_code_sequence (list[dict[str, any]] | None): Department type codes (0008,1041) Type 3
            manufacturers_model_name (str | None): Model name (0008,1090) Type 3
            device_serial_number (str | None): Serial number (0018,1000) Type 3
            date_of_manufacture (str | None): Manufacture date (0018,1204) Type 3
            date_of_installation (str | None): Installation date (0018,1205) Type 3

        Returns:
            dict: Treatment machine sequence item
        """
        item = {
            'TreatmentMachineName': treatment_machine_name
        }

        # Add optional elements if provided
        if manufacturer is not None:
            item['Manufacturer'] = manufacturer
        if institution_name is not None:
            item['InstitutionName'] = institution_name
        if institution_address is not None:
            item['InstitutionAddress'] = institution_address
        if institutional_department_name is not None:
            item['InstitutionalDepartmentName'] = institutional_department_name
        if institutional_department_type_code_sequence is not None:
            item['InstitutionalDepartmentTypeCodeSequence'] = institutional_department_type_code_sequence
        if manufacturers_model_name is not None:
            item['ManufacturersModelName'] = manufacturers_model_name
        if device_serial_number is not None:
            item['DeviceSerialNumber'] = device_serial_number
        if date_of_manufacture is not None:
            item['DateOfManufacture'] = date_of_manufacture
        if date_of_installation is not None:
            item['DateOfInstallation'] = date_of_installation

        return item

    @staticmethod
    def create_source_item(
        source_number: int,
        source_type: str,
        source_isotope_name: str,
        source_isotope_half_life: float,
        reference_air_kerma_rate: float,
        source_strength_reference_date: str,
        source_strength_reference_time: str,
        source_serial_number: str | None = None,
        source_model_id: str | None = None,
        source_description: str | None = None,
        source_manufacturer: str | None = None,
        active_source_diameter: float | None = None,
        active_source_length: float | None = None,
        material_id: str | None = None,
        source_encapsulation_nominal_thickness: float | None = None,
        source_encapsulation_nominal_transmission: float | None = None,
        source_strength_units: str | None = None,
        source_strength: float | None = None
    ) -> dict[str, any]:
        """Create source sequence item.

        Args:
            source_number (int): Source identification number (300A,0212) Type 1
            source_type (str): Type of source (300A,0214) Type 1
            source_isotope_name (str): Name of isotope (300A,0226) Type 1
            source_isotope_half_life (float): Half-life in days (300A,0228) Type 1
            reference_air_kerma_rate (float): Air kerma rate (300A,022A) Type 1
            source_strength_reference_date (str): Reference date (300A,022C) Type 1
            source_strength_reference_time (str): Reference time (300A,022E) Type 1
            source_serial_number (str | None): Source serial number (3008,0105) Type 3
            source_model_id (str | None): Source model ID (300A,021B) Type 3
            source_description (str | None): Source description (300A,021C) Type 3
            source_manufacturer (str | None): Source manufacturer (300A,0216) Type 3
            active_source_diameter (float | None): Active diameter in mm (300A,0218) Type 3
            active_source_length (float | None): Active length in mm (300A,021A) Type 3
            material_id (str | None): Encapsulation material ID (300A,00E1) Type 3
            source_encapsulation_nominal_thickness (float | None): Encapsulation thickness (300A,0222) Type 3
            source_encapsulation_nominal_transmission (float | None): Encapsulation transmission (300A,0224) Type 3
            source_strength_units (str | None): Source strength units (300A,0229) Type 1C
            source_strength (float | None): Source strength (300A,022B) Type 1C

        Returns:
            dict: Source sequence item
        """
        item = {
            'SourceNumber': source_number,
            'SourceType': source_type,
            'SourceIsotopeName': source_isotope_name,
            'SourceIsotopeHalfLife': source_isotope_half_life,
            'ReferenceAirKermaRate': reference_air_kerma_rate,
            'SourceStrengthReferenceDate': source_strength_reference_date,
            'SourceStrengthReferenceTime': source_strength_reference_time
        }

        # Add optional elements if provided
        if source_serial_number is not None:
            item['SourceSerialNumber'] = source_serial_number
        if source_model_id is not None:
            item['SourceModelID'] = source_model_id
        if source_description is not None:
            item['SourceDescription'] = source_description
        if source_manufacturer is not None:
            item['SourceManufacturer'] = source_manufacturer
        if active_source_diameter is not None:
            item['ActiveSourceDiameter'] = active_source_diameter
        if active_source_length is not None:
            item['ActiveSourceLength'] = active_source_length
        if material_id is not None:
            item['MaterialID'] = material_id
        if source_encapsulation_nominal_thickness is not None:
            item['SourceEncapsulationNominalThickness'] = source_encapsulation_nominal_thickness
        if source_encapsulation_nominal_transmission is not None:
            item['SourceEncapsulationNominalTransmission'] = source_encapsulation_nominal_transmission
        if source_strength_units is not None:
            item['SourceStrengthUnits'] = source_strength_units
        if source_strength is not None:
            item['SourceStrength'] = source_strength

        return item

    @property
    def has_brachy_setups(self) -> bool:
        """Check if brachy application setup data is present.
        
        Returns:
            bool: True if Brachy Application Setup Sequence is present
        """
        return hasattr(self, 'BrachyApplicationSetupSequence')
    
    @property
    def brachy_setup_count(self) -> int:
        """Get the number of brachy application setups in this module.
        
        Returns:
            int: Number of setups in Brachy Application Setup Sequence
        """
        setup_sequence = getattr(self, 'BrachyApplicationSetupSequence', [])
        return len(setup_sequence)
    
    def get_brachy_setup_numbers(self) -> list[int]:
        """Get list of brachy application setup numbers present in this module.
        
        Returns:
            list[int]: List of setup numbers
        """
        setup_sequence = getattr(self, 'BrachyApplicationSetupSequence', [])
        setup_numbers = []
        for setup_item in setup_sequence:
            setup_number = setup_item.get('BrachyApplicationSetupNumber')
            if setup_number is not None:
                setup_numbers.append(setup_number)
        return setup_numbers
    
    def get_brachy_setup_by_number(self, setup_number: int) -> dict[str, any] | None:
        """Get brachy application setup by its number.
        
        Args:
            setup_number (int): Setup number to find
            
        Returns:
            dict[str, any] | None: Setup item or None if not found
        """
        setup_sequence = getattr(self, 'BrachyApplicationSetupSequence', [])
        for setup_item in setup_sequence:
            if setup_item.get('BrachyApplicationSetupNumber') == setup_number:
                return setup_item
        return None
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this RT Brachy Application Setups Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return RTBrachyApplicationSetupsValidator.validate(self, config)
