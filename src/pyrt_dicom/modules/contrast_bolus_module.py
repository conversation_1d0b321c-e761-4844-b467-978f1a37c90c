"""
Contrast/Bolus Module - DICOM PS3.3 C.7.6.4

The Contrast/Bolus Module contains attributes that describe the contrast/bolus 
used in the acquisition of the Image.
"""
from datetime import datetime, time
from .base_module import BaseModule
from ..enums.contrast_ct_enums import ContrastBolusIngredient
from ..validators.contrast_bolus_validator import ContrastBolusValidator
from ..validators.base_validator import ValidationConfig


class ContrastBolusModule(BaseModule):
    """Contrast/Bolus Module implementation for DICOM PS3.3 C.7.6.4.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that describe the contrast/bolus agent used in 
    the acquisition of the Image.
    
    Usage:
        # Create with required elements
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )
        
        # Add optional elements
        contrast_bolus.with_optional_elements(
            contrast_bolus_agent_sequence=[
                ContrastBolusModule.create_contrast_agent_code_item(
                    code_value="C-B0322",
                    coding_scheme_designator="SRT",
                    code_meaning="Iodinated contrast agent"
                )
            ],
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="120030",
            contrast_bolus_total_dose=50.0,
            contrast_flow_rate=[5.0],
            contrast_flow_duration=[20.0],
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        )
        
        # Add administration route with additional drugs
        contrast_bolus.with_administration_route(
            administration_route_sequence=[
                ContrastBolusModule.create_administration_route_code_item(
                    code_value="47625008",
                    coding_scheme_designator="SCT",
                    code_meaning="Intravenous route"
                )
            ],
            additional_drug_sequence=[
                ContrastBolusModule.create_additional_drug_code_item(
                    code_value="387467008",
                    coding_scheme_designator="SCT",
                    code_meaning="Saline"
                )
            ]
        )
        
        # Validate
        result = contrast_bolus.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        contrast_bolus_agent: str = ""
    ) -> 'ContrastBolusModule':
        """Create Contrast/Bolus Module with all required (Type 2) elements.
        
        Args:
            contrast_bolus_agent: Contrast or bolus agent (0018,0010) Type 2
        """
        instance = cls()
        
        # Set Type 2 elements
        instance.ContrastBolusAgent = contrast_bolus_agent
        
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        contrast_bolus_agent_sequence: list[dict[str, any]] | None = None,
        contrast_bolus_route: str | None = None,
        contrast_bolus_volume: float | None = None,
        contrast_bolus_start_time: str | time | None = None,
        contrast_bolus_stop_time: str | time | None = None,
        contrast_bolus_total_dose: float | None = None,
        contrast_flow_rate: list[float] | None = None,
        contrast_flow_duration: list[float] | None = None,
        contrast_bolus_ingredient: str | ContrastBolusIngredient | None = None,
        contrast_bolus_ingredient_concentration: float | None = None
    ) -> 'ContrastBolusModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('ContrastBolusAgentSequence', contrast_bolus_agent_sequence)
        self._set_attribute_if_not_none('ContrastBolusRoute', contrast_bolus_route)
        self._set_attribute_if_not_none('ContrastBolusVolume', contrast_bolus_volume)
        
        # Format time values if needed
        if contrast_bolus_start_time is not None:
            formatted_start_time = self._format_time_value(contrast_bolus_start_time)
            self._set_attribute_if_not_none('ContrastBolusStartTime', formatted_start_time)
        
        if contrast_bolus_stop_time is not None:
            formatted_stop_time = self._format_time_value(contrast_bolus_stop_time)
            self._set_attribute_if_not_none('ContrastBolusStopTime', formatted_stop_time)
        
        self._set_attribute_if_not_none('ContrastBolusTotalDose', contrast_bolus_total_dose)
        self._set_attribute_if_not_none('ContrastFlowRate', contrast_flow_rate)
        self._set_attribute_if_not_none('ContrastFlowDuration', contrast_flow_duration)
        # Format enum value if needed
        if contrast_bolus_ingredient is not None:
            formatted_ingredient = self._format_enum_value(contrast_bolus_ingredient)
            self._set_attribute_if_not_none('ContrastBolusIngredient', formatted_ingredient)
        self._set_attribute_if_not_none('ContrastBolusIngredientConcentration', contrast_bolus_ingredient_concentration)
        
        return self
    
    def with_administration_route(
        self,
        administration_route_sequence: list[dict[str, any]] | None = None,
        additional_drug_sequence: list[dict[str, any]] | None = None
    ) -> 'ContrastBolusModule':
        """Add administration route information.
        
        Args:
            administration_route_sequence: Route of administration sequence (0018,0014) Type 3
            additional_drug_sequence: Additional drug sequence (0018,002A) Type 3
        """
        self._set_attribute_if_not_none('ContrastBolusAdministrationRouteSequence', administration_route_sequence)
        
        # Additional drug sequence is nested within administration route sequence
        if administration_route_sequence and additional_drug_sequence:
            for route_item in administration_route_sequence:
                route_item['AdditionalDrugSequence'] = additional_drug_sequence
        
        return self
    
    @staticmethod
    def create_contrast_agent_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> dict[str, any]:
        """Create Contrast/Bolus Agent Sequence code item.
        
        Args:
            code_value: Code value for contrast agent
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            Dictionary representing contrast agent code item
        """
        item = {
            'CodeValue': code_value,
            'CodingSchemeDesignator': coding_scheme_designator
        }
        if code_meaning:
            item['CodeMeaning'] = code_meaning
        return item
    
    @staticmethod
    def create_administration_route_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> dict[str, any]:
        """Create Administration Route Sequence code item.
        
        Args:
            code_value: Code value for administration route
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            Dictionary representing administration route code item
        """
        item = {
            'CodeValue': code_value,
            'CodingSchemeDesignator': coding_scheme_designator
        }
        if code_meaning:
            item['CodeMeaning'] = code_meaning
        return item
    
    @staticmethod
    def create_additional_drug_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> dict[str, any]:
        """Create Additional Drug Sequence code item.
        
        Args:
            code_value: Code value for additional drug
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            Dictionary representing additional drug code item
        """
        item = {
            'CodeValue': code_value,
            'CodingSchemeDesignator': coding_scheme_designator
        }
        if code_meaning:
            item['CodeMeaning'] = code_meaning
        return item
    
    @property
    def has_contrast_agent(self) -> bool:
        """Check if contrast/bolus agent is specified.
        
        Returns:
            bool: True if Contrast/Bolus Agent is present and not empty
        """
        agent = getattr(self, 'ContrastBolusAgent', '')
        return bool(agent)
    
    @property
    def has_agent_sequence(self) -> bool:
        """Check if contrast/bolus agent sequence is present.
        
        Returns:
            bool: True if Contrast/Bolus Agent Sequence is present
        """
        return hasattr(self, 'ContrastBolusAgentSequence')
    
    @property
    def has_administration_route(self) -> bool:
        """Check if administration route information is present.
        
        Returns:
            bool: True if route or route sequence is present
        """
        return (hasattr(self, 'ContrastBolusRoute') or 
                hasattr(self, 'ContrastBolusAdministrationRouteSequence'))
    
    @property
    def has_timing_information(self) -> bool:
        """Check if timing information is present.
        
        Returns:
            bool: True if start time, stop time, or flow duration is present
        """
        return (hasattr(self, 'ContrastBolusStartTime') or 
                hasattr(self, 'ContrastBolusStopTime') or
                hasattr(self, 'ContrastFlowDuration'))
    
    @property
    def has_volume_information(self) -> bool:
        """Check if volume or dose information is present.
        
        Returns:
            bool: True if volume, total dose, or flow rate is present
        """
        return (hasattr(self, 'ContrastBolusVolume') or 
                hasattr(self, 'ContrastBolusTotalDose') or
                hasattr(self, 'ContrastFlowRate'))
    
    @property
    def has_ingredient_information(self) -> bool:
        """Check if ingredient information is present.
        
        Returns:
            bool: True if ingredient or concentration is present
        """
        return (hasattr(self, 'ContrastBolusIngredient') or 
                hasattr(self, 'ContrastBolusIngredientConcentration'))
    
    @property
    def is_iodine_based(self) -> bool:
        """Check if contrast agent is iodine-based.
        
        Returns:
            bool: True if ingredient is IODINE
        """
        ingredient = getattr(self, 'ContrastBolusIngredient', '')
        return ingredient == "IODINE"
    
    @property
    def is_gadolinium_based(self) -> bool:
        """Check if contrast agent is gadolinium-based.
        
        Returns:
            bool: True if ingredient is GADOLINIUM
        """
        ingredient = getattr(self, 'ContrastBolusIngredient', '')
        return ingredient == "GADOLINIUM"
    
    def get_flow_rate_count(self) -> int:
        """Get the number of flow rate values.
        
        Returns:
            int: Number of flow rate values
        """
        flow_rates = getattr(self, 'ContrastFlowRate', [])
        return len(flow_rates) if flow_rates else 0
    
    def get_flow_duration_count(self) -> int:
        """Get the number of flow duration values.
        
        Returns:
            int: Number of flow duration values
        """
        flow_durations = getattr(self, 'ContrastFlowDuration', [])
        return len(flow_durations) if flow_durations else 0
    
    def validate_flow_consistency(self) -> bool:
        """Check if flow rate and duration counts match.
        
        Returns:
            bool: True if flow rate and duration counts are consistent
        """
        rate_count = self.get_flow_rate_count()
        duration_count = self.get_flow_duration_count()
        
        # If both are present, they should have the same count
        if rate_count > 0 and duration_count > 0:
            return rate_count == duration_count
        
        # If only one is present, that's acceptable
        return True
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this Contrast/Bolus Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return ContrastBolusValidator.validate(self, config)
