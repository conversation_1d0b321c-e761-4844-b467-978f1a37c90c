"""
RT Tolerance Tables Module - DICOM PS3.3 C.8.8.11

The RT Tolerance Tables Module contains tolerance tables to be used for delivery 
of treatment plan. Tolerance Tables may be used to compare planned with delivered 
machine parameters.
"""
from .base_module import BaseModule
from ..enums.rt_enums import RTBeamLimitingDeviceType
from ..validators.rt_tolerance_tables_validator import RTToleranceTablesValidator
from ..validators.base_validator import ValidationConfig


class RTToleranceTablesModule(BaseModule):
    """RT Tolerance Tables Module implementation for DICOM PS3.3 C.8.8.11.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains tolerance tables for comparing planned with delivered machine parameters.
    
    Usage:
        # Create with optional elements (no required elements at top level)
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Add optional elements
        tolerance_tables.with_optional_elements(
            tolerance_table_sequence=[
                tolerance_tables.create_tolerance_table_item(
                    tolerance_table_number=1,
                    tolerance_table_label="Standard Tolerances",
                    gantry_angle_tolerance=1.0,
                    beam_limiting_device_tolerance_sequence=[
                        tolerance_tables.create_beam_limiting_device_tolerance_item(
                            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                            beam_limiting_device_position_tolerance=2.0
                        )
                    ]
                )
            ]
        )
        
        # Validate
        result = tolerance_tables.validate()
    """

    @classmethod
    def from_required_elements(
        cls
    ) -> 'RTToleranceTablesModule':
        """Create RTToleranceTablesModule from all required data elements.
        
        Note: This module has no required (Type 1 or Type 2) elements at the top level.
        All elements are Type 3 (optional).
                
        Returns:
            RTToleranceTablesModule: New module instance
        """
        instance = cls()
        return instance
    
    def with_optional_elements(
        self,
        tolerance_table_sequence: list[dict[str, any]] | None = None
    ) -> 'RTToleranceTablesModule':
        """Add optional (Type 3) elements.
        
        Args:
            tolerance_table_sequence (list[dict[str, any]] | None): Sequence of tolerance tables (300A,0040) Type 3
            
        Returns:
            RTToleranceTablesModule: Self for method chaining
        """
        self._set_attribute_if_not_none('ToleranceTableSequence', tolerance_table_sequence)
        return self
    
    @staticmethod
    def create_tolerance_table_item(
        tolerance_table_number: int,
        tolerance_table_label: str | None = None,
        gantry_angle_tolerance: float | None = None,
        gantry_pitch_angle_tolerance: float | None = None,
        beam_limiting_device_angle_tolerance: float | None = None,
        beam_limiting_device_tolerance_sequence: list[dict[str, any]] | None = None,
        patient_support_angle_tolerance: float | None = None,
        table_top_eccentric_angle_tolerance: float | None = None,
        table_top_pitch_angle_tolerance: float | None = None,
        table_top_roll_angle_tolerance: float | None = None,
        table_top_vertical_position_tolerance: float | None = None,
        table_top_longitudinal_position_tolerance: float | None = None,
        table_top_lateral_position_tolerance: float | None = None
    ) -> dict[str, any]:
        """Create tolerance table sequence item.
        
        Args:
            tolerance_table_number (int): Identification number of the Tolerance Table (300A,0042) Type 1
            tolerance_table_label (str | None): User-defined label for Tolerance Table (300A,0043) Type 3
            gantry_angle_tolerance (float | None): Maximum permitted difference in Gantry Angle (300A,0044) Type 3
            gantry_pitch_angle_tolerance (float | None): Maximum permitted difference in Gantry Pitch Angle (300A,014E) Type 3
            beam_limiting_device_angle_tolerance (float | None): Maximum permitted difference in Beam Limiting Device Angle (300A,0046) Type 3
            beam_limiting_device_tolerance_sequence (list[dict[str, any]] | None): Beam limiting device tolerances (300A,0048) Type 3
            patient_support_angle_tolerance (float | None): Maximum permitted difference in Patient Support Angle (300A,004C) Type 3
            table_top_eccentric_angle_tolerance (float | None): Maximum permitted difference in Table Top Eccentric Angle (300A,004E) Type 3
            table_top_pitch_angle_tolerance (float | None): Maximum permitted difference in Table Top Pitch Angle (300A,004F) Type 3
            table_top_roll_angle_tolerance (float | None): Maximum permitted difference in Table Top Roll Angle (300A,0050) Type 3
            table_top_vertical_position_tolerance (float | None): Maximum permitted difference in Table Top Vertical Position (300A,0051) Type 3
            table_top_longitudinal_position_tolerance (float | None): Maximum permitted difference in Table Top Longitudinal Position (300A,0052) Type 3
            table_top_lateral_position_tolerance (float | None): Maximum permitted difference in Table Top Lateral Position (300A,0053) Type 3
            
        Returns:
            dict: Tolerance table sequence item
        """
        item = {
            'ToleranceTableNumber': tolerance_table_number
        }
        
        # Add optional elements if provided
        if tolerance_table_label is not None:
            item['ToleranceTableLabel'] = tolerance_table_label
        if gantry_angle_tolerance is not None:
            item['GantryAngleTolerance'] = gantry_angle_tolerance
        if gantry_pitch_angle_tolerance is not None:
            item['GantryPitchAngleTolerance'] = gantry_pitch_angle_tolerance
        if beam_limiting_device_angle_tolerance is not None:
            item['BeamLimitingDeviceAngleTolerance'] = beam_limiting_device_angle_tolerance
        if beam_limiting_device_tolerance_sequence is not None:
            item['BeamLimitingDeviceToleranceSequence'] = beam_limiting_device_tolerance_sequence
        if patient_support_angle_tolerance is not None:
            item['PatientSupportAngleTolerance'] = patient_support_angle_tolerance
        if table_top_eccentric_angle_tolerance is not None:
            item['TableTopEccentricAngleTolerance'] = table_top_eccentric_angle_tolerance
        if table_top_pitch_angle_tolerance is not None:
            item['TableTopPitchAngleTolerance'] = table_top_pitch_angle_tolerance
        if table_top_roll_angle_tolerance is not None:
            item['TableTopRollAngleTolerance'] = table_top_roll_angle_tolerance
        if table_top_vertical_position_tolerance is not None:
            item['TableTopVerticalPositionTolerance'] = table_top_vertical_position_tolerance
        if table_top_longitudinal_position_tolerance is not None:
            item['TableTopLongitudinalPositionTolerance'] = table_top_longitudinal_position_tolerance
        if table_top_lateral_position_tolerance is not None:
            item['TableTopLateralPositionTolerance'] = table_top_lateral_position_tolerance
        
        return item
    
    @staticmethod
    def create_beam_limiting_device_tolerance_item(
        rt_beam_limiting_device_type: str | RTBeamLimitingDeviceType,
        beam_limiting_device_position_tolerance: float
    ) -> dict[str, any]:
        """Create beam limiting device tolerance sequence item.
        
        Args:
            rt_beam_limiting_device_type (str | RTBeamLimitingDeviceType): Type of beam limiting device (300A,00B8) Type 1
            beam_limiting_device_position_tolerance (float): Maximum permitted difference in positions (300A,004A) Type 1
            
        Returns:
            dict: Beam limiting device tolerance sequence item
        """
        return {
            'RTBeamLimitingDeviceType': rt_beam_limiting_device_type.value if hasattr(rt_beam_limiting_device_type, 'value') else str(rt_beam_limiting_device_type),
            'BeamLimitingDevicePositionTolerance': beam_limiting_device_position_tolerance
        }
    
    @property
    def has_tolerance_tables(self) -> bool:
        """Check if tolerance tables are present.
        
        Returns:
            bool: True if Tolerance Table Sequence is present
        """
        return hasattr(self, 'ToleranceTableSequence')
    
    @property
    def tolerance_table_count(self) -> int:
        """Get the number of tolerance tables in this module.
        
        Returns:
            int: Number of tolerance tables in Tolerance Table Sequence
        """
        tolerance_sequence = getattr(self, 'ToleranceTableSequence', [])
        return len(tolerance_sequence)
    
    def get_tolerance_table_numbers(self) -> list[int]:
        """Get list of tolerance table numbers present in this module.
        
        Returns:
            list[int]: List of tolerance table numbers
        """
        tolerance_sequence = getattr(self, 'ToleranceTableSequence', [])
        table_numbers = []
        for table_item in tolerance_sequence:
            table_number = table_item.get('ToleranceTableNumber')
            if table_number is not None:
                table_numbers.append(table_number)
        return table_numbers
    
    def get_tolerance_table_by_number(self, table_number: int) -> dict[str, any] | None:
        """Get tolerance table by its number.
        
        Args:
            table_number (int): Tolerance table number to find
            
        Returns:
            dict[str, any] | None: Tolerance table item or None if not found
        """
        tolerance_sequence = getattr(self, 'ToleranceTableSequence', [])
        for table_item in tolerance_sequence:
            if table_item.get('ToleranceTableNumber') == table_number:
                return table_item
        return None
    
    def has_angular_tolerances(self) -> bool:
        """Check if any tolerance table has angular tolerances defined.
        
        Returns:
            bool: True if any angular tolerance is present
        """
        tolerance_sequence = getattr(self, 'ToleranceTableSequence', [])
        for table_item in tolerance_sequence:
            angular_fields = [
                'GantryAngleTolerance',
                'GantryPitchAngleTolerance', 
                'BeamLimitingDeviceAngleTolerance',
                'PatientSupportAngleTolerance',
                'TableTopEccentricAngleTolerance',
                'TableTopPitchAngleTolerance',
                'TableTopRollAngleTolerance'
            ]
            if any(table_item.get(field) is not None for field in angular_fields):
                return True
        return False
    
    def has_positional_tolerances(self) -> bool:
        """Check if any tolerance table has positional tolerances defined.
        
        Returns:
            bool: True if any positional tolerance is present
        """
        tolerance_sequence = getattr(self, 'ToleranceTableSequence', [])
        for table_item in tolerance_sequence:
            positional_fields = [
                'TableTopVerticalPositionTolerance',
                'TableTopLongitudinalPositionTolerance',
                'TableTopLateralPositionTolerance'
            ]
            if any(table_item.get(field) is not None for field in positional_fields):
                return True
            
            # Check beam limiting device position tolerances
            bld_tolerance_seq = table_item.get('BeamLimitingDeviceToleranceSequence', [])
            if any(bld_item.get('BeamLimitingDevicePositionTolerance') is not None for bld_item in bld_tolerance_seq):
                return True
        return False
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this RT Tolerance Tables Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return RTToleranceTablesValidator.validate(self, config)
