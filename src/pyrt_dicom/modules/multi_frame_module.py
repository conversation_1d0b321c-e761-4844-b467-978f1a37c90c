"""
Multi-frame Module - DICOM PS3.3 C.7.6.6

The Multi-frame Module describes a Multi-frame pixel data Image.
"""
from .base_module import BaseModule
from ..enums.image_enums import StereoPairsPresent
from ..validators.multi_frame_validator import MultiFrameValidator
from ..validators.base_validator import ValidationConfig


class MultiFrameModule(BaseModule):
    """Multi-frame Module implementation for DICOM PS3.3 C.7.6.6.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes a Multi-frame pixel data Image.
    
    Usage:
        # Create with required elements
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=["0018,1063"]  # Frame Time
        )
        
        # Add optional elements
        multi_frame.with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO,
            encapsulated_pixel_data_value_total_length=1024000
        )
        
        # Validate
        result = multi_frame.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        number_of_frames: int,
        frame_increment_pointer: list[str]
    ) -> 'MultiFrameModule':
        """Create MultiFrameModule from all required (Type 1) data elements.
        
        Args:
            number_of_frames (int): Number of frames in multi-frame image (0028,0008) Type 1.
                Must be greater than zero.
            frame_increment_pointer (list[str]): Data element tags for frame sequencing (0028,0009) Type 1.
                Each value should be a DICOM tag in format "GGGG,EEEE" that points to an attribute
                used as the frame increment in multi-frame pixel data.
            
        Returns:
            MultiFrameModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.NumberOfFrames = number_of_frames
        instance.FrameIncrementPointer = frame_increment_pointer
        return instance
    
    def with_optional_elements(
        self,
        stereo_pairs_present: str | StereoPairsPresent | None = None,
        encapsulated_pixel_data_value_total_length: int | None = None
    ) -> 'MultiFrameModule':
        """Add optional (Type 3) data elements.
        
        Args:
            stereo_pairs_present (str | StereoPairsPresent | None): Stereoscopic pairs flag (0022,0028) Type 3.
                Indicates if multi-frame pixel data consists of left and right stereoscopic pairs.
            encapsulated_pixel_data_value_total_length (int | None): Total pixel data length (7FE0,0003) Type 3.
                Length of pixel data bit stream in bytes when all fragments are combined.
            
        Returns:
            MultiFrameModule: Self with optional elements added
        """
        if stereo_pairs_present is not None:
            self.StereoPairsPresent = self._format_enum_value(stereo_pairs_present)
        self._set_attribute_if_not_none('EncapsulatedPixelDataValueTotalLength', encapsulated_pixel_data_value_total_length)
        return self
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time() -> list[str]:
        """Create frame increment pointer for Frame Time attribute.
        
        Returns:
            list[str]: Frame increment pointer pointing to Frame Time (0018,1063)
        """
        return ["0018,1063"]
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time_vector() -> list[str]:
        """Create frame increment pointer for Frame Time Vector attribute.
        
        Returns:
            list[str]: Frame increment pointer pointing to Frame Time Vector (0018,1065)
        """
        return ["0018,1065"]
    
    @staticmethod
    def create_frame_increment_pointer_for_functional_groups() -> list[str]:
        """Create frame increment pointer for Per-Frame Functional Groups.
        
        Returns:
            list[str]: Frame increment pointer pointing to Per-Frame Functional Groups Sequence (5200,9230)
        """
        return ["5200,9230"]
    
    @property
    def is_single_frame(self) -> bool:
        """Check if this is actually a single frame (edge case)."""
        return hasattr(self, 'NumberOfFrames') and self.NumberOfFrames == 1
    
    @property
    def is_multi_frame(self) -> bool:
        """Check if this is truly multi-frame."""
        return hasattr(self, 'NumberOfFrames') and self.NumberOfFrames > 1
    
    @property
    def has_stereo_pairs(self) -> bool:
        """Check if stereoscopic pairs are present."""
        return hasattr(self, 'StereoPairsPresent') and self.StereoPairsPresent == "YES"
    
    @property
    def expected_stereo_frame_count(self) -> int | None:
        """Calculate expected number of stereo frame pairs."""
        if not self.has_stereo_pairs or not hasattr(self, 'NumberOfFrames'):
            return None
        # For stereo pairs, frames come in pairs (left/right)
        return self.NumberOfFrames // 2
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        return "0018,1063" in self.FrameIncrementPointer
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        return "0018,1065" in self.FrameIncrementPointer
    
    @property
    def uses_functional_groups(self) -> bool:
        """Check if Per-Frame Functional Groups are used for frame increment."""
        if not hasattr(self, 'FrameIncrementPointer'):
            return False
        return "5200,9230" in self.FrameIncrementPointer
    
    def get_frame_increment_attributes(self) -> list[str]:
        """Get list of frame increment attribute names.
        
        Returns:
            list[str]: List of DICOM attribute names referenced by frame increment pointer
        """
        if not hasattr(self, 'FrameIncrementPointer'):
            return []
        
        # Map common tags to attribute names
        tag_to_attr = {
            "0018,1063": "FrameTime",
            "0018,1065": "FrameTimeVector", 
            "5200,9230": "PerFrameFunctionalGroupsSequence"
        }
        
        attributes = []
        for tag in self.FrameIncrementPointer:
            if tag in tag_to_attr:
                attributes.append(tag_to_attr[tag])
            else:
                # For unknown tags, try to convert to attribute name format
                group, element = tag.split(',')
                # This is a simplified conversion - real implementation would need full tag dictionary
                attributes.append(f"Tag{group}{element}")
        
        return attributes
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return MultiFrameValidator.validate(self, config)
