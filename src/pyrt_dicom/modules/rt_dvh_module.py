"""
RT DVH Module - DICOM PS3.3 C.8.8.4

The RT DVH Module provides for the inclusion of differential or cumulative 
dose volume histogram data.
"""
from .base_module import BaseModule
from ..enums.dose_enums import DoseUnits, DoseType
from ..enums.rt_enums import DVHType, DVHROIContributionType, DVHVolumeUnits
from ..validators.rt_dvh_validator import RTDVHValidator
from ..validators.base_validator import ValidationConfig


class RTDVHModule(BaseModule):
    """RT DVH Module implementation for DICOM PS3.3 C.8.8.4.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Provides for inclusion of differential or cumulative dose volume histogram data.
    
    Usage:
        # Create with required elements
        dvh = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[
                dvh.create_referenced_structure_set_item(
                    referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.3",
                    referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
                )
            ],
            dvh_sequence=[
                dvh.create_dvh_item(
                    dvh_referenced_roi_sequence=[
                        dvh.create_dvh_referenced_roi_item(
                            referenced_roi_number=1,
                            dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                        )
                    ],
                    dvh_type=DVHType.CUMULATIVE,
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dvh_dose_scaling=1.0,
                    dvh_volume_units=DVHVolumeUnits.CM3,
                    dvh_number_of_bins=100,
                    dvh_data=[0.1, 10.5, 0.2, 9.8]  # D1V1, D2V2, ...
                )
            ]
        )
        
        # Add optional elements
        dvh.with_optional_elements(
            dvh_normalization_point=[100.0, 50.0, 25.0],
            dvh_normalization_dose_value=200.0
        )
        
        # Validate
        result = dvh.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        referenced_structure_set_sequence: list[dict[str, any]],
        dvh_sequence: list[dict[str, any]]
    ) -> 'RTDVHModule':
        """Create RTDVHModule from all required (Type 1) data elements.
        
        Args:
            referenced_structure_set_sequence (list[dict[str, any]]): Structure Set containing structures for DVHs (300C,0060) Type 1
            dvh_sequence (list[dict[str, any]]): Sequence of DVHs (3004,0050) Type 1
                
        Returns:
            RTDVHModule: New module instance with required data elements set
        """
        instance = cls()
        instance.ReferencedStructureSetSequence = referenced_structure_set_sequence
        instance.DVHSequence = dvh_sequence
        return instance
    
    def with_optional_elements(
        self,
        dvh_normalization_point: list[float] | None = None,
        dvh_normalization_dose_value: float | None = None
    ) -> 'RTDVHModule':
        """Add optional (Type 3) elements.
        
        Args:
            dvh_normalization_point (list[float] | None): Coordinates (x,y,z) of common DVH normalization point (3004,0040) Type 3
            dvh_normalization_dose_value (float | None): Dose Value at DVH Normalization Point (3004,0042) Type 3
            
        Returns:
            RTDVHModule: Self for method chaining
        """
        self._set_attribute_if_not_none('DVHNormalizationPoint', dvh_normalization_point)
        self._set_attribute_if_not_none('DVHNormalizationDoseValue', dvh_normalization_dose_value)
        return self
    
    @staticmethod
    def create_referenced_structure_set_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> dict[str, any]:
        """Create referenced structure set sequence item.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            
        Returns:
            dict: Referenced structure set sequence item
        """
        return {
            'ReferencedSOPClassUID': referenced_sop_class_uid,
            'ReferencedSOPInstanceUID': referenced_sop_instance_uid
        }
    
    @staticmethod
    def create_dvh_item(
        dvh_referenced_roi_sequence: list[dict[str, any]],
        dvh_type: str | DVHType,
        dose_units: str | DoseUnits,
        dose_type: str | DoseType,
        dvh_dose_scaling: float,
        dvh_volume_units: str | DVHVolumeUnits,
        dvh_number_of_bins: int,
        dvh_data: list[float],
        dvh_minimum_dose: float | None = None,
        dvh_maximum_dose: float | None = None,
        dvh_mean_dose: float | None = None
    ) -> dict[str, any]:
        """Create DVH sequence item.
        
        Args:
            dvh_referenced_roi_sequence (list[dict[str, any]]): Referenced ROIs used to calculate DVH (3004,0060) Type 1
            dvh_type (str | DVHType): Type of DVH (3004,0001) Type 1
            dose_units (str | DoseUnits): Dose axis units (3004,0002) Type 1
            dose_type (str | DoseType): Type of dose (3004,0004) Type 1
            dvh_dose_scaling (float): Scaling factor for dose bin widths (3004,0052) Type 1
            dvh_volume_units (str | DVHVolumeUnits): Volume axis units (3004,0054) Type 1
            dvh_number_of_bins (int): Number of bins used to store DVH Data (3004,0056) Type 1
            dvh_data (list[float]): Data stream describing dose bin widths and volumes (3004,0058) Type 1
            dvh_minimum_dose (float | None): Minimum calculated dose to ROI(s) (3004,0070) Type 3
            dvh_maximum_dose (float | None): Maximum calculated dose to ROI(s) (3004,0072) Type 3
            dvh_mean_dose (float | None): Mean calculated dose to ROI(s) (3004,0074) Type 3
            
        Returns:
            dict: DVH sequence item
        """
        item = {
            'DVHReferencedROISequence': dvh_referenced_roi_sequence,
            'DVHType': dvh_type.value if hasattr(dvh_type, 'value') else str(dvh_type),
            'DoseUnits': dose_units.value if hasattr(dose_units, 'value') else str(dose_units),
            'DoseType': dose_type.value if hasattr(dose_type, 'value') else str(dose_type),
            'DVHDoseScaling': dvh_dose_scaling,
            'DVHVolumeUnits': dvh_volume_units.value if hasattr(dvh_volume_units, 'value') else str(dvh_volume_units),
            'DVHNumberOfBins': dvh_number_of_bins,
            'DVHData': dvh_data
        }
        
        # Add optional elements if provided
        if dvh_minimum_dose is not None:
            item['DVHMinimumDose'] = dvh_minimum_dose
        if dvh_maximum_dose is not None:
            item['DVHMaximumDose'] = dvh_maximum_dose
        if dvh_mean_dose is not None:
            item['DVHMeanDose'] = dvh_mean_dose
        
        return item
    
    @staticmethod
    def create_dvh_referenced_roi_item(
        referenced_roi_number: int,
        dvh_roi_contribution_type: str | DVHROIContributionType
    ) -> dict[str, any]:
        """Create DVH referenced ROI sequence item.
        
        Args:
            referenced_roi_number (int): ROI Number used to calculate DVH (3006,0084) Type 1
            dvh_roi_contribution_type (str | DVHROIContributionType): Whether volume is included or excluded (3004,0062) Type 1
            
        Returns:
            dict: DVH referenced ROI sequence item
        """
        return {
            'ReferencedROINumber': referenced_roi_number,
            'DVHROIContributionType': dvh_roi_contribution_type.value if hasattr(dvh_roi_contribution_type, 'value') else str(dvh_roi_contribution_type)
        }
    
    @property
    def has_normalization_info(self) -> bool:
        """Check if DVH normalization information is present.
        
        Returns:
            bool: True if normalization point or dose value is present
        """
        return (hasattr(self, 'DVHNormalizationPoint') or 
                hasattr(self, 'DVHNormalizationDoseValue'))
    
    @property
    def dvh_count(self) -> int:
        """Get the number of DVHs in this module.
        
        Returns:
            int: Number of DVHs in DVH Sequence
        """
        dvh_sequence = getattr(self, 'DVHSequence', [])
        return len(dvh_sequence)
    
    @property
    def total_roi_count(self) -> int:
        """Get the total number of ROIs referenced across all DVHs.
        
        Returns:
            int: Total number of ROI references
        """
        dvh_sequence = getattr(self, 'DVHSequence', [])
        total_rois = 0
        for dvh_item in dvh_sequence:
            roi_sequence = dvh_item.get('DVHReferencedROISequence', [])
            total_rois += len(roi_sequence)
        return total_rois
    
    def get_dvh_types(self) -> list[str]:
        """Get list of DVH types present in this module.
        
        Returns:
            list[str]: List of unique DVH types
        """
        dvh_sequence = getattr(self, 'DVHSequence', [])
        dvh_types = []
        for dvh_item in dvh_sequence:
            dvh_type = dvh_item.get('DVHType', '')
            if dvh_type and dvh_type not in dvh_types:
                dvh_types.append(dvh_type)
        return dvh_types
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this RT DVH Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return RTDVHValidator.validate(self, config)
