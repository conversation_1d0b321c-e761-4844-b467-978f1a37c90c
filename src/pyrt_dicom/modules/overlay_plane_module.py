"""
Overlay Plane Module - DICOM PS3.3 C.9.2

The Overlay Plane Module describes characteristics of an Overlay Plane.
An Overlay Plane describes graphics or bit-mapped text that is associated with an Image.
"""
from .base_module import BaseModule
from ..enums.image_enums import OverlayType, OverlaySubtype
from ..validators.overlay_plane_validator import OverlayPlaneValidator
from ..validators.base_validator import ValidationConfig


class OverlayPlaneModule(BaseModule):
    """Overlay Plane Module implementation for DICOM PS3.3 C.9.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes characteristics of an Overlay Plane that contains graphics 
    or bit-mapped text associated with an Image.
    
    Note: This implementation uses group 6000 for overlay attributes.
    DICOM allows groups 6000-601E (even groups only) for multiple overlays.
    
    Usage:
        # Create with required elements
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=512,
            overlay_columns=512,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=b'...'  # 1-bit overlay data
        )
        
        # Add optional elements
        overlay.with_optional_elements(
            overlay_description="ROI boundary",
            overlay_subtype=OverlaySubtype.USER,
            overlay_label="Tumor boundary"
        )
        
        # Add ROI statistics if overlay type is ROI
        overlay.with_roi_statistics(
            roi_area=1024,
            roi_mean=150.5,
            roi_standard_deviation=25.3
        )
        
        # Validate
        result = overlay.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        overlay_rows: int,
        overlay_columns: int,
        overlay_type: str | OverlayType,
        overlay_origin: list[int],
        overlay_data: bytes,
        overlay_group: int = 0x6000
    ) -> 'OverlayPlaneModule':
        """Create OverlayPlaneModule from all required (Type 1) data elements.
        
        Args:
            overlay_rows (int): Number of rows in overlay (60xx,0010) Type 1
            overlay_columns (int): Number of columns in overlay (60xx,0011) Type 1
            overlay_type (str | OverlayType): Overlay type - Graphics or ROI (60xx,0040) Type 1
            overlay_origin (list[int]): Location of first overlay point [row, column] (60xx,0050) Type 1.
                1-based coordinates relative to image pixels.
            overlay_data (bytes): Overlay pixel data (60xx,3000) Type 1.
                1-bit overlay data, left to right, top to bottom.
            overlay_group (int): DICOM group number for overlay (default: 0x6000).
                Must be even number between 0x6000 and 0x601E.
            
        Returns:
            OverlayPlaneModule: New dataset instance with required data elements set
        """
        instance = cls()
        
        # Validate overlay group
        if overlay_group < 0x6000 or overlay_group > 0x601E or overlay_group % 2 != 0:
            raise ValueError("Overlay group must be even number between 0x6000 and 0x601E")
        
        # Set attributes using the specified group
        # Note: pydicom handles group-specific attributes automatically
        instance.OverlayRows = overlay_rows
        instance.OverlayColumns = overlay_columns
        instance.OverlayType = instance._format_enum_value(overlay_type)
        instance.OverlayOrigin = overlay_origin
        instance.OverlayBitsAllocated = 1  # Always 1 for overlays
        instance.OverlayBitPosition = 0    # Always 0 for overlays
        instance.OverlayData = overlay_data
        
        # Store group for reference
        instance._overlay_group = overlay_group
        
        return instance
    
    def with_optional_elements(
        self,
        overlay_description: str | None = None,
        overlay_subtype: str | OverlaySubtype | None = None,
        overlay_label: str | None = None
    ) -> 'OverlayPlaneModule':
        """Add optional (Type 3) data elements.
        
        Args:
            overlay_description (str | None): User-defined comments about overlay (60xx,0022) Type 3
            overlay_subtype (str | OverlaySubtype | None): Overlay purpose identifier (60xx,0045) Type 3
            overlay_label (str | None): User-defined text label for overlay (60xx,1500) Type 3
            
        Returns:
            OverlayPlaneModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('OverlayDescription', overlay_description)
        if overlay_subtype is not None:
            self.OverlaySubtype = self._format_enum_value(overlay_subtype)
        self._set_attribute_if_not_none('OverlayLabel', overlay_label)
        return self
    
    def with_roi_statistics(
        self,
        roi_area: int | None = None,
        roi_mean: float | None = None,
        roi_standard_deviation: float | None = None
    ) -> 'OverlayPlaneModule':
        """Add ROI statistical parameters (Type 3, relevant when overlay type is ROI).
        
        Args:
            roi_area (int | None): Number of pixels in ROI area (60xx,1301) Type 3
            roi_mean (float | None): ROI mean pixel value (60xx,1302) Type 3
            roi_standard_deviation (float | None): ROI standard deviation (60xx,1303) Type 3
            
        Returns:
            OverlayPlaneModule: Self with ROI statistics added
        """
        self._set_attribute_if_not_none('ROIArea', roi_area)
        self._set_attribute_if_not_none('ROIMean', roi_mean)
        self._set_attribute_if_not_none('ROIStandardDeviation', roi_standard_deviation)
        return self
    
    @staticmethod
    def calculate_overlay_data_size(rows: int, columns: int) -> int:
        """Calculate expected overlay data size in bytes.
        
        Args:
            rows (int): Number of overlay rows
            columns (int): Number of overlay columns
            
        Returns:
            int: Expected data size in bytes (padded to even length)
        """
        total_bits = rows * columns
        total_bytes = (total_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length
        return total_bytes + (total_bytes % 2)
    
    @property
    def is_graphics_overlay(self) -> bool:
        """Check if this is a graphics overlay."""
        return hasattr(self, 'OverlayType') and self.OverlayType == "G"
    
    @property
    def is_roi_overlay(self) -> bool:
        """Check if this is an ROI overlay."""
        return hasattr(self, 'OverlayType') and self.OverlayType == "R"
    
    @property
    def has_roi_statistics(self) -> bool:
        """Check if ROI statistics are present."""
        return any(hasattr(self, attr) for attr in ['ROIArea', 'ROIMean', 'ROIStandardDeviation'])
    
    @property
    def overlay_pixel_count(self) -> int | None:
        """Calculate total number of overlay pixels."""
        if not all(hasattr(self, attr) for attr in ['OverlayRows', 'OverlayColumns']):
            return None
        return self.OverlayRows * self.OverlayColumns
    
    @property
    def expected_data_size(self) -> int | None:
        """Calculate expected overlay data size."""
        pixel_count = self.overlay_pixel_count
        if pixel_count is None:
            return None
        return self.calculate_overlay_data_size(self.OverlayRows, self.OverlayColumns)
    
    @property
    def actual_data_size(self) -> int | None:
        """Get actual overlay data size."""
        if not hasattr(self, 'OverlayData'):
            return None
        return len(self.OverlayData)
    
    @property
    def overlay_group(self) -> int:
        """Get the overlay group number."""
        return getattr(self, '_overlay_group', 0x6000)
    
    def get_overlay_origin_coordinates(self) -> tuple[int, int] | None:
        """Get overlay origin as (row, column) tuple.
        
        Returns:
            tuple[int, int] | None: (row, column) coordinates or None if not available
        """
        if not hasattr(self, 'OverlayOrigin') or len(self.OverlayOrigin) != 2:
            return None
        return (self.OverlayOrigin[0], self.OverlayOrigin[1])
    
    def is_overlay_within_image(self, image_rows: int, image_columns: int) -> bool:
        """Check if overlay fits within image boundaries.
        
        Args:
            image_rows (int): Number of rows in associated image
            image_columns (int): Number of columns in associated image
            
        Returns:
            bool: True if overlay fits within image boundaries
        """
        origin = self.get_overlay_origin_coordinates()
        if origin is None or not all(hasattr(self, attr) for attr in ['OverlayRows', 'OverlayColumns']):
            return False
        
        origin_row, origin_col = origin
        
        # Calculate overlay boundaries
        overlay_end_row = origin_row + self.OverlayRows - 1
        overlay_end_col = origin_col + self.OverlayColumns - 1
        
        # Check if overlay extends beyond image
        return (origin_row >= 1 and origin_col >= 1 and
                overlay_end_row <= image_rows and overlay_end_col <= image_columns)
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return OverlayPlaneValidator.validate(self, config)
