"""
Frame Extraction Module - DICOM PS3.3 C.12.3

The Frame Extraction Module describes the Frames extracted if the SOP Instance
was created in response to a Frame-Level retrieve request.
"""
from typing import Optional, Union, List, Dict, Any
from .base_module import BaseModule
from ..validators.frame_extraction_validator import FrameExtractionValidator
from ..validators.base_validator import ValidationConfig


class FrameExtractionModule(BaseModule):
    """Frame Extraction Module implementation for DICOM PS3.3 C.12.3.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes the Frames extracted if the SOP Instance was created in response
    to a Frame-Level retrieve request.
    
    Usage:
        # Create with Simple Frame List
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="1.2.3.4.5.6.7.8.9",
                    simple_frame_list=[1, 3, 5, 7]
                )
            ]
        )
        
        # Create with Calculated Frame List
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="1.2.3.4.5.6.7.8.9",
                    calculated_frame_list=[1, 10, 2]  # start, end, increment
                )
            ]
        )
        
        # Create with Time Range
        frame_extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid="1.2.3.4.5.6.7.8.9",
                    time_range=["120000", "130000"]  # start and end times
                )
            ]
        )
        
        # Validate
        result = frame_extraction.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        frame_extraction_sequence: List[Dict[str, Any]]
    ) -> 'FrameExtractionModule':
        """Create module with required elements.
        
        Args:
            frame_extraction_sequence: Sequence containing details of frame extraction
            
        Returns:
            FrameExtractionModule: New module instance with required data elements set
        """
        if not frame_extraction_sequence:
            raise ValueError("Frame Extraction Sequence must contain at least one item")
        
        instance = cls()
        instance.FrameExtractionSequence = frame_extraction_sequence
        return instance
    
    @staticmethod
    def create_frame_extraction_item(
        multi_frame_source_sop_instance_uid: str,
        simple_frame_list: Optional[List[int]] = None,
        calculated_frame_list: Optional[List[int]] = None,
        time_range: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Create a Frame Extraction Sequence item.
        
        Exactly one of simple_frame_list, calculated_frame_list, or time_range must be provided.
        
        Args:
            multi_frame_source_sop_instance_uid: SOP Instance from which frames are extracted
            simple_frame_list: List of frames extracted as simple list
            calculated_frame_list: List of frames as triplets [start, end, increment]
            time_range: Start and end times of extracted frames
            
        Returns:
            Dict representing a Frame Extraction Sequence item
        """
        # Validate that exactly one frame specification is provided
        frame_specs = [simple_frame_list, calculated_frame_list, time_range]
        provided_specs = [spec for spec in frame_specs if spec is not None]
        
        if len(provided_specs) != 1:
            raise ValueError("Exactly one of simple_frame_list, calculated_frame_list, or time_range must be provided")
        
        item = {
            'MultiFrameSourceSOPInstanceUID': multi_frame_source_sop_instance_uid
        }
        
        if simple_frame_list is not None:
            item['SimpleFrameList'] = simple_frame_list
        elif calculated_frame_list is not None:
            if len(calculated_frame_list) % 3 != 0:
                raise ValueError("Calculated Frame List must contain triplets (start, end, increment)")
            item['CalculatedFrameList'] = calculated_frame_list
        elif time_range is not None:
            if len(time_range) != 2:
                raise ValueError("Time Range must contain exactly two values (start, end)")
            item['TimeRange'] = time_range
        
        return item
    
    def add_frame_extraction_item(
        self,
        multi_frame_source_sop_instance_uid: str,
        simple_frame_list: Optional[List[int]] = None,
        calculated_frame_list: Optional[List[int]] = None,
        time_range: Optional[List[str]] = None
    ) -> 'FrameExtractionModule':
        """Add another Frame Extraction Sequence item.
        
        Args:
            multi_frame_source_sop_instance_uid: SOP Instance from which frames are extracted
            simple_frame_list: List of frames extracted as simple list
            calculated_frame_list: List of frames as triplets [start, end, increment]
            time_range: Start and end times of extracted frames
            
        Returns:
            FrameExtractionModule: Self for method chaining
        """
        new_item = self.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid,
            simple_frame_list,
            calculated_frame_list,
            time_range
        )
        
        if not hasattr(self, 'FrameExtractionSequence'):
            self.FrameExtractionSequence = []
        
        self.FrameExtractionSequence.append(new_item)
        return self
    
    @property
    def extraction_count(self) -> int:
        """Get the number of frame extraction items.
        
        Returns:
            int: Number of frame extraction sequence items
        """
        if hasattr(self, 'FrameExtractionSequence'):
            return len(self.FrameExtractionSequence)
        return 0
    
    @property
    def has_simple_frame_lists(self) -> bool:
        """Check if any items use Simple Frame List.
        
        Returns:
            bool: True if any item contains Simple Frame List
        """
        if not hasattr(self, 'FrameExtractionSequence'):
            return False
        
        return any('SimpleFrameList' in item for item in self.FrameExtractionSequence)
    
    @property
    def has_calculated_frame_lists(self) -> bool:
        """Check if any items use Calculated Frame List.
        
        Returns:
            bool: True if any item contains Calculated Frame List
        """
        if not hasattr(self, 'FrameExtractionSequence'):
            return False
        
        return any('CalculatedFrameList' in item for item in self.FrameExtractionSequence)
    
    @property
    def has_time_ranges(self) -> bool:
        """Check if any items use Time Range.
        
        Returns:
            bool: True if any item contains Time Range
        """
        if not hasattr(self, 'FrameExtractionSequence'):
            return False
        
        return any('TimeRange' in item for item in self.FrameExtractionSequence)
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if Frame Extraction Sequence is present and not empty
        """
        return hasattr(self, 'FrameExtractionSequence') and len(self.FrameExtractionSequence) > 0
    
    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this Frame Extraction Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return FrameExtractionValidator.validate(self, config)
