"""
RT Fraction Scheme Module - DICOM PS3.3 C.8.8.13

The RT Fraction Scheme Module contains information describing the fractionation 
of the treatment plan. It specifies the number of fractions and the beams or 
brachy application setups to be used in each fraction group.
"""
from .base_module import BaseModule
from ..enums.rt_enums import BeamDoseMeaning, DoseCalibrationConditionsVerifiedFlag
from ..enums.dose_enums import DoseType
from ..validators.rt_fraction_scheme_validator import RTFractionSchemeValidator
from ..validators.base_validator import ValidationConfig


class RTFractionSchemeModule(BaseModule):
    """RT Fraction Scheme Module implementation for DICOM PS3.3 C.8.8.13.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains information describing the fractionation of the treatment plan.
    
    Usage:
        # Create with required elements
        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[
                fraction_scheme.create_fraction_group_item(
                    fraction_group_number=1,
                    number_of_fractions_planned=25,
                    number_of_beams=4,
                    number_of_brachy_application_setups=0,
                    referenced_beam_sequence=[
                        fraction_scheme.create_referenced_beam_item(
                            referenced_beam_number=1,
                            beam_dose=200.0
                        )
                    ]
                )
            ]
        )
        
        # Validate
        result = fraction_scheme.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        fraction_group_sequence: list[dict[str, any]]
    ) -> 'RTFractionSchemeModule':
        """Create RTFractionSchemeModule from all required (Type 1) data elements.
        
        Args:
            fraction_group_sequence (list[dict[str, any]]): Sequence of fraction groups (300A,0070) Type 1
                
        Returns:
            RTFractionSchemeModule: New module instance with required data elements set
        """
        instance = cls()
        instance.FractionGroupSequence = fraction_group_sequence
        return instance
    
    def with_optional_elements(
        self
    ) -> 'RTFractionSchemeModule':
        """Add optional (Type 3) elements.
        
        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the Fraction Group Sequence.
        
        Returns:
            RTFractionSchemeModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_fraction_group_item(
        fraction_group_number: int,
        number_of_fractions_planned: int = 0,
        number_of_beams: int = 0,
        number_of_brachy_application_setups: int = 0,
        fraction_group_description: str | None = None,
        definition_source_sequence: list[dict[str, any]] | None = None,
        referenced_dose_sequence: list[dict[str, any]] | None = None,
        referenced_dose_reference_sequence: list[dict[str, any]] | None = None,
        number_of_fraction_pattern_digits_per_day: int | None = None,
        fraction_pattern: str | None = None,
        repeat_fraction_cycle_length: int | None = None,
        beam_dose_meaning: str | BeamDoseMeaning | None = None,
        referenced_beam_sequence: list[dict[str, any]] | None = None,
        referenced_brachy_application_setup_sequence: list[dict[str, any]] | None = None
    ) -> dict[str, any]:
        """Create fraction group sequence item.

        Args:
            fraction_group_number (int): Identification number of the Fraction Group (300A,0071) Type 1
            number_of_fractions_planned (int): Number of fractions planned for this group (300A,0078) Type 2
            number_of_beams (int): Number of beams in this fraction group (300A,0080) Type 1
            number_of_brachy_application_setups (int): Number of brachy setups (300A,00A0) Type 1
            fraction_group_description (str | None): Description of fraction group (300A,0072) Type 3
            definition_source_sequence (list[dict[str, any]] | None): Definition source sequence (0008,1156) Type 3
            referenced_dose_sequence (list[dict[str, any]] | None): Referenced dose sequence (300C,0080) Type 3
            referenced_dose_reference_sequence (list[dict[str, any]] | None): Referenced dose reference sequence (300C,0050) Type 3
            number_of_fraction_pattern_digits_per_day (int | None): Digits per day in pattern (300A,0079) Type 1C
            fraction_pattern (str | None): Pattern of fraction delivery (300A,007B) Type 1C
            repeat_fraction_cycle_length (int | None): Length of repeating cycle (300A,007C) Type 1C
            beam_dose_meaning (str | BeamDoseMeaning | None): Meaning of beam dose (300A,008B) Type 3
            referenced_beam_sequence (list[dict[str, any]] | None): Beams in this fraction group (300C,0004) Type 1C
            referenced_brachy_application_setup_sequence (list[dict[str, any]] | None): Brachy setups (300C,000A) Type 1C

        Returns:
            dict: Fraction group sequence item
        """
        # Validate conditional requirements
        if number_of_beams > 0 and referenced_beam_sequence is None:
            raise ValueError("Referenced Beam Sequence is required when Number of Beams > 0")
        
        if number_of_brachy_application_setups > 0 and referenced_brachy_application_setup_sequence is None:
            raise ValueError("Referenced Brachy Application Setup Sequence is required when Number of Brachy Application Setups > 0")
        
        item = {
            'FractionGroupNumber': fraction_group_number,
            'NumberOfFractionsPlanned': number_of_fractions_planned,
            'NumberOfBeams': number_of_beams,
            'NumberOfBrachyApplicationSetups': number_of_brachy_application_setups
        }
        
        # Add conditional elements
        if referenced_beam_sequence is not None:
            item['ReferencedBeamSequence'] = referenced_beam_sequence
        if referenced_brachy_application_setup_sequence is not None:
            item['ReferencedBrachyApplicationSetupSequence'] = referenced_brachy_application_setup_sequence
        
        # Add optional elements if provided
        if fraction_group_description is not None:
            item['FractionGroupDescription'] = fraction_group_description
        if definition_source_sequence is not None:
            item['DefinitionSourceSequence'] = definition_source_sequence
        if referenced_dose_sequence is not None:
            item['ReferencedDoseSequence'] = referenced_dose_sequence
        if referenced_dose_reference_sequence is not None:
            item['ReferencedDoseReferenceSequence'] = referenced_dose_reference_sequence
        if number_of_fraction_pattern_digits_per_day is not None:
            item['NumberOfFractionPatternDigitsPerDay'] = number_of_fraction_pattern_digits_per_day
        if fraction_pattern is not None:
            item['FractionPattern'] = fraction_pattern
        if repeat_fraction_cycle_length is not None:
            item['RepeatFractionCycleLength'] = repeat_fraction_cycle_length
        if beam_dose_meaning is not None:
            item['BeamDoseMeaning'] = beam_dose_meaning.value if hasattr(beam_dose_meaning, 'value') else str(beam_dose_meaning)
        
        return item
    
    @staticmethod
    def create_referenced_beam_item(
        referenced_beam_number: int,
        beam_dose: float | None = None,
        beam_meterset: float | None = None,
        beam_dose_meaning: str | BeamDoseMeaning | None = None,
        beam_dose_type: str | DoseType | None = None,
        beam_dose_specification_point: list[float] | None = None,
        beam_dose_verification_control_point_sequence: list[dict[str, any]] | None = None,
        average_beam_dose_point_depth: float | None = None,
        average_beam_dose_point_equivalent_depth: float | None = None,
        average_beam_dose_point_ssd: float | None = None,
        beam_dose_point_depth: float | None = None,
        beam_dose_point_equivalent_depth: float | None = None,
        beam_dose_point_ssd: float | None = None
    ) -> dict[str, any]:
        """Create referenced beam sequence item.
        
        Args:
            referenced_beam_number (int): Beam Number within RT Plan (300C,0006) Type 1
            beam_dose (float | None): Dose delivered by this beam (300A,0084) Type 3
            beam_meterset (float | None): Meterset for this beam (300A,0086) Type 3
            beam_dose_meaning (str | BeamDoseMeaning | None): Meaning of beam dose (300A,008B) Type 1C
            beam_dose_type (str | DoseType | None): Type of beam dose (300A,008C) Type 1C
            beam_dose_specification_point (list[float] | None): Point where dose is specified (300A,0088) Type 1C
            beam_dose_verification_control_point_sequence (list[dict[str, any]] | None): Verification points (300A,008A) Type 3
            average_beam_dose_point_depth (float | None): Average depth of dose point (300A,0090) Type 3
            average_beam_dose_point_equivalent_depth (float | None): Average equivalent depth (300A,0091) Type 3
            average_beam_dose_point_ssd (float | None): Average SSD of dose point (300A,0092) Type 3
            beam_dose_point_depth (float | None): Depth of dose point (300A,0093) Type 3
            beam_dose_point_equivalent_depth (float | None): Equivalent depth of dose point (300A,0094) Type 3
            beam_dose_point_ssd (float | None): SSD of dose point (300A,0095) Type 3
            
        Returns:
            dict: Referenced beam sequence item
        """
        item = {
            'ReferencedBeamNumber': referenced_beam_number
        }
        
        # Add optional elements if provided
        if beam_dose is not None:
            item['BeamDose'] = beam_dose
        if beam_meterset is not None:
            item['BeamMeterset'] = beam_meterset
        if beam_dose_meaning is not None:
            item['BeamDoseMeaning'] = beam_dose_meaning.value if hasattr(beam_dose_meaning, 'value') else str(beam_dose_meaning)
        if beam_dose_type is not None:
            item['BeamDoseType'] = beam_dose_type.value if hasattr(beam_dose_type, 'value') else str(beam_dose_type)
        if beam_dose_specification_point is not None:
            item['BeamDoseSpecificationPoint'] = beam_dose_specification_point
        if beam_dose_verification_control_point_sequence is not None:
            item['BeamDoseVerificationControlPointSequence'] = beam_dose_verification_control_point_sequence
        if average_beam_dose_point_depth is not None:
            item['AverageBeamDosePointDepth'] = average_beam_dose_point_depth
        if average_beam_dose_point_equivalent_depth is not None:
            item['AverageBeamDosePointEquivalentDepth'] = average_beam_dose_point_equivalent_depth
        if average_beam_dose_point_ssd is not None:
            item['AverageBeamDosePointSSD'] = average_beam_dose_point_ssd
        if beam_dose_point_depth is not None:
            item['BeamDosePointDepth'] = beam_dose_point_depth
        if beam_dose_point_equivalent_depth is not None:
            item['BeamDosePointEquivalentDepth'] = beam_dose_point_equivalent_depth
        if beam_dose_point_ssd is not None:
            item['BeamDosePointSSD'] = beam_dose_point_ssd
        
        return item
    
    @staticmethod
    def create_referenced_brachy_application_setup_item(
        referenced_brachy_application_setup_number: int,
        brachy_application_setup_dose: float | None = None,
        brachy_application_setup_dose_specification_point: list[float] | None = None
    ) -> dict[str, any]:
        """Create referenced brachy application setup sequence item.
        
        Args:
            referenced_brachy_application_setup_number (int): Setup Number within RT Plan (300C,000C) Type 1
            brachy_application_setup_dose (float | None): Dose delivered by setup (300A,00A4) Type 3
            brachy_application_setup_dose_specification_point (list[float] | None): Point where dose is specified (300A,00A6) Type 1C
            
        Returns:
            dict: Referenced brachy application setup sequence item
        """
        item = {
            'ReferencedBrachyApplicationSetupNumber': referenced_brachy_application_setup_number
        }
        
        # Add optional elements if provided
        if brachy_application_setup_dose is not None:
            item['BrachyApplicationSetupDose'] = brachy_application_setup_dose
        if brachy_application_setup_dose_specification_point is not None:
            item['BrachyApplicationSetupDoseSpecificationPoint'] = brachy_application_setup_dose_specification_point
        
        return item

    @staticmethod
    def create_referenced_dose_item(
        referenced_dose_reference_number: int
    ) -> dict[str, any]:
        """Create referenced dose sequence item.

        Args:
            referenced_dose_reference_number (int): Dose Reference Number (300A,0012) Type 1

        Returns:
            dict: Referenced dose sequence item
        """
        return {
            'ReferencedDoseReferenceNumber': referenced_dose_reference_number
        }

    @staticmethod
    def create_referenced_dose_reference_item(
        referenced_dose_reference_number: int
    ) -> dict[str, any]:
        """Create referenced dose reference sequence item.

        Args:
            referenced_dose_reference_number (int): Dose Reference Number (300A,0012) Type 1

        Returns:
            dict: Referenced dose reference sequence item
        """
        return {
            'ReferencedDoseReferenceNumber': referenced_dose_reference_number
        }

    @staticmethod
    def create_definition_source_item(
        definition_source_description: str | None = None,
        definition_source_detail_sequence: list[dict[str, any]] | None = None
    ) -> dict[str, any]:
        """Create definition source sequence item.

        Args:
            definition_source_description (str | None): Description of definition source Type 3
            definition_source_detail_sequence (list[dict[str, any]] | None): Detail sequence Type 3

        Returns:
            dict: Definition source sequence item
        """
        item = {}

        if definition_source_description is not None:
            item['DefinitionSourceDescription'] = definition_source_description
        if definition_source_detail_sequence is not None:
            item['DefinitionSourceDetailSequence'] = definition_source_detail_sequence

        return item

    @property
    def has_fraction_groups(self) -> bool:
        """Check if fraction groups are present.
        
        Returns:
            bool: True if Fraction Group Sequence is present
        """
        return hasattr(self, 'FractionGroupSequence')
    
    @property
    def fraction_group_count(self) -> int:
        """Get the number of fraction groups in this module.
        
        Returns:
            int: Number of fraction groups in Fraction Group Sequence
        """
        fraction_sequence = getattr(self, 'FractionGroupSequence', [])
        return len(fraction_sequence)
    
    def get_total_fractions_planned(self) -> int:
        """Get the total number of fractions planned across all groups.
        
        Returns:
            int: Total number of fractions planned
        """
        fraction_sequence = getattr(self, 'FractionGroupSequence', [])
        total_fractions = 0
        for fraction_item in fraction_sequence:
            fractions_planned = fraction_item.get('NumberOfFractionsPlanned', 0)
            total_fractions += fractions_planned
        return total_fractions
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this RT Fraction Scheme Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return RTFractionSchemeValidator.validate(self, config)
