"""
Clinical Trial Series Module - DICOM PS3.3 C.7.3.2

The Clinical Trial Series Module contains attributes that identify a Series 
in the context of a clinical trial or research.
"""
from typing import Optional, List, Dict, Any
from .base_module import BaseModule
from ..validators.base_validator import ValidationConfig
from ..validators.clinical_trial_series_validator import ClinicalTrialSeriesValidator


class ClinicalTrialSeriesModule(BaseModule):
    """Clinical Trial Series Module implementation for DICOM PS3.3 C.7.3.2.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify a Series in the context of a clinical trial or research.
    
    Usage:
        # Create with required elements (all Type 3, so empty instance is valid)
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Add optional elements
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="Research Center",
            clinical_trial_series_id="SERIES001",
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        # Validate
        result = trial_series.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'ClinicalTrialSeriesModule':
        """Create ClinicalTrialSeriesModule from all required (Type 1 and Type 2) data elements.
        
        Note: All elements in this module are Type 3, so no required elements.
        
        Returns:
            ClinicalTrialSeriesModule: New empty dataset instance
        """
        return cls()
    
    def with_optional_elements(
        self,
        clinical_trial_coordinating_center_name: Optional[str] = None,
        clinical_trial_series_id: Optional[str] = None,
        issuer_of_clinical_trial_series_id: Optional[str] = None,
        clinical_trial_series_description: Optional[str] = None
    ) -> 'ClinicalTrialSeriesModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            clinical_trial_coordinating_center_name (str | None): Name of coordinating center (0012,0060) Type 3
            clinical_trial_series_id (str | None): Identifier for the Series (0012,0071) Type 3
            issuer_of_clinical_trial_series_id (str | None): Assigning Authority for series ID (0012,0072) Type 3
            clinical_trial_series_description (str | None): Description of the Series (0012,0073) Type 3
            
        Returns:
            ClinicalTrialSeriesModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('ClinicalTrialCoordinatingCenterName', clinical_trial_coordinating_center_name)
        self._set_attribute_if_not_none('ClinicalTrialSeriesID', clinical_trial_series_id)
        self._set_attribute_if_not_none('IssuerOfClinicalTrialSeriesID', issuer_of_clinical_trial_series_id)
        self._set_attribute_if_not_none('ClinicalTrialSeriesDescription', clinical_trial_series_description)
        return self
    
    @property
    def has_coordinating_center_info(self) -> bool:
        """Check if coordinating center information is present.
        
        Returns:
            bool: True if coordinating center name is present
        """
        return hasattr(self, 'ClinicalTrialCoordinatingCenterName')
    
    @property
    def has_series_identification(self) -> bool:
        """Check if series identification information is present.
        
        Returns:
            bool: True if series ID or description is present
        """
        return any(hasattr(self, attr) for attr in [
            'ClinicalTrialSeriesID', 'ClinicalTrialSeriesDescription'
        ])
    
    @property
    def has_issuer_info(self) -> bool:
        """Check if issuer information is present.
        
        Returns:
            bool: True if issuer of series ID is present
        """
        return hasattr(self, 'IssuerOfClinicalTrialSeriesID')
    
    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this Clinical Trial Series Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return ClinicalTrialSeriesValidator.validate(self, config)
