"""
Frame of Reference Module - DICOM PS3.3 C.7.4.1

The Frame of Reference Module contains attributes necessary to uniquely identify 
a Frame of Reference that ensures the spatial relationship of Images within a Series.
"""
from .base_module import BaseModule
from ..validators.frame_of_reference_validator import FrameOfReferenceValidator
from ..validators.base_validator import ValidationConfig


class FrameOfReferenceModule(BaseModule):
    """Frame of Reference Module implementation for DICOM PS3.3 C.7.4.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes necessary to uniquely identify a Frame of Reference 
    that ensures the spatial relationship of Images within a Series.
    
    Usage:
        # Create with required elements
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="*******.*******.9",
            position_reference_indicator="STERNAL_NOTCH"
        )
        
        # Validate
        result = frame_ref.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        frame_of_reference_uid: str,
        position_reference_indicator: str = ""
    ) -> 'FrameOfReferenceModule':
        """Create FrameOfReferenceModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            frame_of_reference_uid (str): Unique identifier for Frame of Reference (0020,0052) Type 1.
                Must be a valid DICOM UID that uniquely identifies the Frame of Reference for a Series.
            position_reference_indicator (str): Reference point for Frame of Reference (0020,1040) Type 2.
                Part of imaging target used as reference point. Can be anatomical reference point
                (e.g., "STERNAL_NOTCH", "ILIAC_CREST") or special values like "SLIDE_CORNER",
                "CORNEAL_VERTEX_R", "CORNEAL_VERTEX_L". May be zero length when no meaningful
                reference point exists.
            
        Returns:
            FrameOfReferenceModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.FrameOfReferenceUID = frame_of_reference_uid
        instance.PositionReferenceIndicator = position_reference_indicator
        return instance
    
    @staticmethod
    def create_anatomical_reference_indicators() -> dict[str, str]:
        """Get common anatomical reference indicators for patient-related frames of reference.
        
        Returns:
            dict[str, str]: Dictionary of reference indicator codes and descriptions
        """
        return {
            "ILIAC_CREST": "Iliac crest anatomical landmark",
            "ORBITAL_MEDIAL": "Orbital-medial anatomical landmark", 
            "STERNAL_NOTCH": "Sternal notch anatomical landmark",
            "SYMPHYSIS_PUBIS": "Symphysis pubis anatomical landmark",
            "XIPHOID": "Xiphoid process anatomical landmark",
            "LOWER_COSTAL_MARGIN": "Lower costal margin anatomical landmark",
            "EXTERNAL_AUDITORY_MEATUS": "External auditory meatus anatomical landmark"
        }
    
    @staticmethod
    def create_slide_reference_indicator() -> str:
        """Get slide corner reference indicator for slide-related frames of reference.
        
        Returns:
            str: Slide corner reference indicator
        """
        return "SLIDE_CORNER"
    
    @staticmethod
    def create_corneal_reference_indicators() -> dict[str, str]:
        """Get corneal vertex reference indicators for corneal coordinate systems.
        
        Returns:
            dict[str, str]: Dictionary of corneal reference indicators
        """
        return {
            "CORNEAL_VERTEX_R": "Corneal vertex for right eye",
            "CORNEAL_VERTEX_L": "Corneal vertex for left eye"
        }
    
    @property
    def is_patient_based(self) -> bool:
        """Check if this appears to be a patient-based frame of reference."""
        if not hasattr(self, 'PositionReferenceIndicator'):
            return False
        
        anatomical_indicators = self.create_anatomical_reference_indicators()
        return self.PositionReferenceIndicator in anatomical_indicators
    
    @property
    def is_slide_based(self) -> bool:
        """Check if this is a slide-based frame of reference."""
        return (hasattr(self, 'PositionReferenceIndicator') and 
                self.PositionReferenceIndicator == "SLIDE_CORNER")
    
    @property
    def is_corneal_based(self) -> bool:
        """Check if this is a corneal coordinate system frame of reference."""
        if not hasattr(self, 'PositionReferenceIndicator'):
            return False
        
        corneal_indicators = self.create_corneal_reference_indicators()
        return self.PositionReferenceIndicator in corneal_indicators
    
    @property
    def corneal_eye_side(self) -> str | None:
        """Get the eye side for corneal coordinate systems.
        
        Returns:
            str | None: "RIGHT", "LEFT", or None if not corneal-based
        """
        if not self.is_corneal_based:
            return None
        
        if self.PositionReferenceIndicator == "CORNEAL_VERTEX_R":
            return "RIGHT"
        elif self.PositionReferenceIndicator == "CORNEAL_VERTEX_L":
            return "LEFT"
        
        return None
    
    @property
    def has_meaningful_reference(self) -> bool:
        """Check if position reference indicator has meaningful content."""
        return (hasattr(self, 'PositionReferenceIndicator') and 
                len(self.PositionReferenceIndicator.strip()) > 0)
    
    @property
    def reference_type(self) -> str:
        """Get the type of reference frame.
        
        Returns:
            str: "PATIENT", "SLIDE", "CORNEAL", "UNKNOWN", or "EMPTY"
        """
        if not hasattr(self, 'PositionReferenceIndicator'):
            return "UNKNOWN"
        
        if len(self.PositionReferenceIndicator.strip()) == 0:
            return "EMPTY"
        elif self.is_patient_based:
            return "PATIENT"
        elif self.is_slide_based:
            return "SLIDE"
        elif self.is_corneal_based:
            return "CORNEAL"
        else:
            return "UNKNOWN"
    
    def is_compatible_with(self, other: 'FrameOfReferenceModule') -> bool:
        """Check if this frame of reference is compatible with another.
        
        Args:
            other (FrameOfReferenceModule): Other frame of reference to compare
            
        Returns:
            bool: True if frames of reference are compatible (same UID)
        """
        if not isinstance(other, FrameOfReferenceModule):
            return False
        
        return (hasattr(self, 'FrameOfReferenceUID') and 
                hasattr(other, 'FrameOfReferenceUID') and
                self.FrameOfReferenceUID == other.FrameOfReferenceUID)
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return FrameOfReferenceValidator.validate(self, config)
