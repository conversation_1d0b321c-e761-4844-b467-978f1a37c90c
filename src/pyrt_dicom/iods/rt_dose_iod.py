"""RT Dose IOD implementation for DICOM PS3.3 A.18.3.

This demonstrates a much simpler approach than multiple inheritance:
- Inherits only from SimpleBaseIOD (which inherits from FileDataset)
- Uses composition for modules (patient_module, rt_dose_module, etc.)
- Maintains IntelliSense through property delegation
- Much cleaner and easier to understand
"""

from typing import Optional, List, Dict, Any
import numpy as np
from pydicom.uid import generate_uid

from .base_iod import BaseIOD
from ..modules.patient_module import PatientModule


class RTDose(BaseIOD):
    """RT Dose IOD implementation for DICOM PS3.3 A.18.3.
    
    Clean implementation using composition instead of multiple inheritance.
    Much simpler and more maintainable approach.
    
    Required Modules (per DICOM spec):
    - Patient Module (C.7.1.1) - M
    - General Study Module (C.7.2.1) - M  
    - RT Series Module (C.8.8.1) - M
    - Frame of Reference Module (C.7.4.1) - M
    - General Equipment Module (C.7.5.1) - M
    - RT Dose Module (C.8.8.3) - M
    - SOP Common Module (C.12.1) - M
    
    Usage:
        # Create RT Dose with patient data
        rt_dose = RTDose.from_required_modules(
            patient_module=patient,
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL
        )
        
        # Add dose grid data
        rt_dose.with_dose_grid_data(
            pixel_data=dose_array.tobytes(),
            rows=50, columns=50,
            pixel_spacing=[2.0, 2.0],
            image_position_patient=[-50.0, -50.0, 0.0]
        )
        
        # Validate and save
        result = rt_dose.validate()
        rt_dose.save_as("dose.dcm")
    """
    
    SOP_CLASS_UID = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
    
    def __init__(self, **kwargs):
        """Initialize RT Dose IOD."""
        super().__init__(filename="rtdose.dcm")
        
        # Module composition (not inheritance!)
        self.patient_module: Optional[PatientModule] = None
        
        # Set RT Dose specific defaults
        self.Modality = "RTDOSE"
        
        # Set any provided attributes
        for key, value in kwargs.items():
            if hasattr(self, key) or not key.startswith('_'):
                setattr(self, key, value)
        
        # Generate required UIDs
        if not hasattr(self, 'StudyInstanceUID'):
            self.StudyInstanceUID = generate_uid()
        if not hasattr(self, 'SeriesInstanceUID'):
            self.SeriesInstanceUID = generate_uid()
        if not hasattr(self, 'FrameOfReferenceUID'):
            self.FrameOfReferenceUID = generate_uid()
    
    @classmethod
    def from_required_modules(
        cls,
        patient_module: PatientModule,
        **kwargs
    ) -> 'RTDose':
        """Create RT Dose from patient module and essential dose data.
        
        Args:
            patient_module: PatientModule instance with patient data
            dose_units: Units for dose values (3004,0002) Type 1
            dose_type: Type of dose (3004,0004) Type 1
            **kwargs: Additional DICOM data element values
            
        Returns:
            RTDose: New RT Dose IOD instance
        """
        instance = cls(**kwargs)
        
        # Add patient module using composition
        instance.add_module("patient", patient_module)
        
        return instance
    
    def _validate_iod_requirements(self, result: Dict[str, List[str]]) -> None:
        """Validate RT Dose specific requirements."""
        super()._validate_iod_requirements(result)
        
        # RT Dose Module validation
        if not hasattr(self, 'DoseUnits') or not self.DoseUnits:
            result["errors"].append("Dose Units (3004,0002) is required (Type 1)")
        elif self.DoseUnits not in ["GY", "RELATIVE"]:
            result["warnings"].append(f"Dose Units (3004,0002) value '{self.DoseUnits}' should be GY or RELATIVE")
        
        if not hasattr(self, 'DoseType') or not self.DoseType:
            result["errors"].append("Dose Type (3004,0004) is required (Type 1)")
        elif self.DoseType not in ["PHYSICAL", "EFFECTIVE", "ERROR"]:
            result["warnings"].append(f"Dose Type (3004,0004) value '{self.DoseType}' should be PHYSICAL, EFFECTIVE, or ERROR")
        
        # Required UIDs
        required_uids = [
            ('StudyInstanceUID', 'Study Instance UID'),
            ('SeriesInstanceUID', 'Series Instance UID'),
            ('FrameOfReferenceUID', 'Frame of Reference UID')
        ]
        
        for attr_name, display_name in required_uids:
            if not hasattr(self, attr_name) or not getattr(self, attr_name):
                result["errors"].append(f"{display_name} is required")
        
        # Modality validation
        if not hasattr(self, 'Modality') or self.Modality != "RTDOSE":
            result["errors"].append("Modality must be 'RTDOSE' for RT Dose IOD")
        
        # Patient module validation
        if not self.patient_module:
            result["errors"].append("Patient Module is required for RT Dose IOD")
    
    def __repr__(self) -> str:
        """String representation of the RT Dose IOD."""
        return f"SimpleRTDose(patient='{self.patient_module}', dose_units='{getattr(self, 'DoseUnits', 'None')}', sop_instance_uid='{self.SOPInstanceUID}')"