"""
Image Module Enums - Enumerated values for image-related DICOM modules.

This module contains enumerated values used across various image modules
including General Image, Image Pixel, Multi-frame, Cine, and Overlay Plane modules.
"""
from enum import Enum


class QualityControlImage(Enum):
    """Quality Control Image enumerated values (0028,0300)."""
    YES = "YES"  # Image contains only quality control material
    NO = "NO"    # Image does not contain quality control material
    BOTH = "BOTH"  # Image contains both subject and quality control information


class BurnedInAnnotation(Enum):
    """Burned In Annotation enumerated values (0028,0301)."""
    YES = "YES"  # Image contains sufficient burned in annotation
    NO = "NO"    # Image does not contain burned in annotation


class RecognizableVisualFeatures(Enum):
    """Recognizable Visual Features enumerated values (0028,0302)."""
    YES = "YES"  # Image contains sufficiently recognizable visual features
    NO = "NO"    # Image does not contain recognizable visual features


class LossyImageCompression(Enum):
    """Lossy Image Compression enumerated values (0028,2110)."""
    NOT_COMPRESSED = "00"  # Image has not been subjected to lossy compression
    COMPRESSED = "01"      # Image has been subjected to lossy compression


class PresentationLUTShape(Enum):
    """Presentation LUT Shape enumerated values (2050,0020)."""
    IDENTITY = "IDENTITY"  # Output is in P-Values
    INVERSE = "INVERSE"    # Output after inversion is in P-Values


class ImageLaterality(Enum):
    """Image Laterality enumerated values (0020,0062)."""
    RIGHT = "R"      # Right
    LEFT = "L"       # Left
    UNPAIRED = "U"   # Unpaired
    BOTH = "B"       # Both left and right


class PhotometricInterpretation(Enum):
    """Photometric Interpretation enumerated values (0028,0004)."""
    MONOCHROME1 = "MONOCHROME1"      # Single monochrome, min=white
    MONOCHROME2 = "MONOCHROME2"      # Single monochrome, min=black
    PALETTE_COLOR = "PALETTE COLOR"   # Color with palette lookup
    RGB = "RGB"                      # Red, green, blue color
    YBR_FULL = "YBR_FULL"           # Luminance and chrominance
    YBR_FULL_422 = "YBR_FULL_422"   # YBR with horizontal chrominance subsampling
    YBR_PARTIAL_420 = "YBR_PARTIAL_420"  # YBR with horizontal and vertical subsampling
    YBR_ICT = "YBR_ICT"             # Irreversible Color Transformation
    YBR_RCT = "YBR_RCT"             # Reversible Color Transformation
    XYB = "XYB"                     # XYB color model


class PlanarConfiguration(Enum):
    """Planar Configuration enumerated values (0028,0006)."""
    COLOR_BY_PIXEL = "0"  # Color-by-pixel (R1,G1,B1,R2,G2,B2,...)
    COLOR_BY_PLANE = "1"  # Color-by-plane (R1,R2,R3,...,G1,G2,G3,...,B1,B2,B3,...)


class PixelRepresentation(Enum):
    """Pixel Representation enumerated values (0028,0103)."""
    UNSIGNED = "0000H"  # Unsigned integer
    SIGNED = "0001H"    # 2's complement


class StereoPairsPresent(Enum):
    """Stereo Pairs Present enumerated values (0022,0028)."""
    YES = "YES"  # Multi-frame image consists of stereoscopic pairs
    NO = "NO"    # Multi-frame image does not consist of stereoscopic pairs


class PreferredPlaybackSequencing(Enum):
    """Preferred Playback Sequencing enumerated values (0018,1244)."""
    LOOPING = "0"   # Looping (1,2,...,n,1,2,...,n,1,2,...,n,...)
    SWEEPING = "1"  # Sweeping (1,2,...,n,n-1,...,2,1,2,...,n,...)


class ChannelMode(Enum):
    """Channel Mode enumerated values (003A,0302)."""
    MONO = "MONO"      # 1 signal
    STEREO = "STEREO"  # 2 simultaneously acquired (left and right) signals


class OverlayType(Enum):
    """Overlay Type enumerated values (60xx,0040)."""
    GRAPHICS = "G"  # Graphics overlay
    ROI = "R"       # Region of Interest overlay


class OverlaySubtype(Enum):
    """Overlay Subtype enumerated values (60xx,0045)."""
    USER = "USER"                    # User created graphic annotation
    AUTOMATED = "AUTOMATED"          # Machine or algorithm generated annotation
    ACTIVE_IMAGE_AREA = "ACTIVE IMAGE AREA"  # Identification of active area


class LossyImageCompressionMethod(Enum):
    """Lossy Image Compression Method enumerated values (0028,2114)."""
    ISO_10918_1 = "ISO_10918_1"      # JPEG Lossy Compression
    ISO_14495_1 = "ISO_14495_1"      # JPEG-LS Near-lossless Compression
    ISO_15444_1 = "ISO_15444_1"      # JPEG 2000 Irreversible Compression
    ISO_15444_15 = "ISO_15444_15"    # High-Throughput JPEG 2000 Irreversible
    ISO_18181_1 = "ISO_18181_1"      # JPEG XL Image Coding System
    ISO_13818_2 = "ISO_13818_2"      # MPEG2 Compression
    ISO_14496_10 = "ISO_14496_10"    # MPEG-4 AVC/H.264 Compression
    ISO_23008_2 = "ISO_23008_2"      # HEVC/H.265 Lossy Compression


class ImageType(Enum):
    """Image Type enumerated values for Value 1 and 2 (0008,0008)."""
    # Value 1 - Pixel Data Characteristics
    ORIGINAL = "ORIGINAL"  # Original Image
    DERIVED = "DERIVED"    # Derived Image

    # Value 2 - Patient Examination Characteristics
    PRIMARY = "PRIMARY"    # Primary Image
    SECONDARY = "SECONDARY"  # Secondary Image


class ModalityLutType(Enum):
    """Modality LUT Type enumerated values (0028,3004)."""
    OD = "OD"              # Optical density (thousands)
    HU = "HU"              # Hounsfield Units (CT)
    US = "US"              # Unspecified
    MGML = "MGML"          # mg/ml
    Z_EFF = "Z_EFF"        # Effective Atomic Number
    ED = "ED"              # Electron density in 10^23 electrons/ml
    EDW = "EDW"            # Electron density normalized to water
    HU_MOD = "HU_MOD"      # Modified Hounsfield Unit
    PCT = "PCT"            # Percentage (%)


class RescaleType(Enum):
    """Rescale Type enumerated values (0028,1054)."""
    OD = "OD"              # Optical density (thousands)
    HU = "HU"              # Hounsfield Units (CT)
    US = "US"              # Unspecified
    MGML = "MGML"          # mg/ml
    Z_EFF = "Z_EFF"        # Effective Atomic Number
    ED = "ED"              # Electron density in 10^23 electrons/ml
    EDW = "EDW"            # Electron density normalized to water
    HU_MOD = "HU_MOD"      # Modified Hounsfield Unit
    PCT = "PCT"            # Percentage (%)


class VoiLutFunction(Enum):
    """VOI LUT Function enumerated values (0028,1056)."""
    LINEAR = "LINEAR"              # Default linear function
    LINEAR_EXACT = "LINEAR_EXACT"  # Exact linear function
    SIGMOID = "SIGMOID"            # Sigmoid function
