"""Radiotherapy-specific DICOM enumerations."""

from enum import Enum



class ConversionType(Enum):
    """Conversion Type (0008,0064) - DICOM VR: CS
    
    Describes the kind of image conversion.
    """
    DV = "DV"    # Digitized Video
    DI = "DI"    # Digital Interface
    DF = "DF"    # Digitized Film
    WSD = "WSD"  # Workstation


class BeamType(Enum):
    """Beam Type (300A,00C4) - DICOM VR: CS
    Motion characteristic of Beam.
    """
    STATIC = "STATIC"   # All Control Point Sequence remain unchanged between pairs with changing Cumulative Meterset Weight
    DYNAMIC = "DYNAMIC" # One or more Control Point Sequence change between pairs with changing Cumulative Meterset Weight


class RadiationType(Enum):
    """Radiation Type (300A,00C6) - DICOM VR: CS
    Particle type of Beam.
    """
    PHOTON = "PHOTON"
    ELECTRON = "ELECTRON"
    NEUTRON = "NEUTRON"
    PROTON = "PROTON"


class ReportedValuesOrigin(Enum):
    """Reported Values Origin (3002,000A) - DICOM VR: CS
    
    Describes the origin of the parameter values reported in the image.
    """
    OPERATOR = "OPERATOR"  # Manually entered by operator
    PLAN = "PLAN"         # Planned parameter values
    ACTUAL = "ACTUAL"     # Electronically recorded


class RTImagePlane(Enum):
    """RT Image Plane (3002,000C) - DICOM VR: CS
    
    Describes whether or not image plane is normal to beam axis.
    """
    NORMAL = "NORMAL"         # Image plane normal to beam axis
    NON_NORMAL = "NON_NORMAL" # Image plane non-normal to beam axis


class PrimaryDosimeterUnit(Enum):
    """Primary Dosimeter Unit (300A,00B3) - DICOM VR: CS
    
    Measurement unit of machine dosimeter.
    """
    MU = "MU"         # Monitor Unit
    MINUTE = "MINUTE" # Minute


class PixelIntensityRelationshipSign(Enum):
    """Pixel Intensity Relationship Sign (0028,1041) - DICOM VR: SS
    
    The sign of the relationship between pixel sample values and X-Ray beam intensity.
    """
    POSITIVE = "+1"  # Lower pixel values correspond to less X-Ray beam intensity
    NEGATIVE = "-1"  # Higher pixel values correspond to less X-Ray beam intensity


class FluenceMode(Enum):
    """Fluence Mode (3002,0051) - DICOM VR: CS
    
    Describes whether the fluence shaping is the standard mode for the beam or an alternate.
    """
    STANDARD = "STANDARD"         # Uses standard fluence-shaping
    NON_STANDARD = "NON_STANDARD" # Uses a non-standard fluence-shaping mode


class RTBeamLimitingDeviceType(Enum):
    """RT Beam Limiting Device Type (300A,00B8) - DICOM VR: CS
    
    Type of beam limiting device (collimator).
    """
    X = "X"         # Symmetric jaw pair in IEC X direction
    Y = "Y"         # Symmetric jaw pair in IEC Y direction
    ASYMX = "ASYMX" # Asymmetric jaw pair in IEC X direction
    ASYMY = "ASYMY" # Asymmetric jaw pair in IEC Y direction
    MLCX = "MLCX"   # Single layer multileaf collimator in IEC X direction
    MLCY = "MLCY"   # Single layer multileaf collimator in IEC Y direction


class ApplicatorType(Enum):
    """Applicator Type (300A,0109) - DICOM VR: CS
    
    Type of Applicator.
    """
    ELECTRON_SQUARE = "ELECTRON_SQUARE"   # Square electron applicator
    ELECTRON_RECT = "ELECTRON_RECT"       # Rectangular electron applicator
    ELECTRON_CIRC = "ELECTRON_CIRC"       # Circular electron applicator
    ELECTRON_SHORT = "ELECTRON_SHORT"     # Short electron applicator
    ELECTRON_OPEN = "ELECTRON_OPEN"       # Open (dummy) electron applicator
    PHOTON_SQUARE = "PHOTON_SQUARE"       # Square photon applicator
    PHOTON_RECT = "PHOTON_RECT"           # Rectangular photon applicator
    PHOTON_CIRC = "PHOTON_CIRC"           # Circular photon applicator
    INTRAOPERATIVE = "INTRAOPERATIVE"     # Intraoperative (custom) applicator
    STEREOTACTIC = "STEREOTACTIC"         # Stereotactic applicator (deprecated)


class ApplicatorApertureShape(Enum):
    """Applicator Aperture Shape (300A,0432) - DICOM VR: CS
    
    Aperture shape of the applicator.
    """
    SYM_SQUARE = "SYM_SQUARE"         # Square-shaped aperture symmetrical to central axis
    SYM_RECTANGLE = "SYM_RECTANGLE"   # Rectangular-shaped aperture symmetrical to central axis
    SYM_CIRCULAR = "SYM_CIRCULAR"     # Circular-shaped aperture symmetrical to central axis


class GeneralAccessoryType(Enum):
    """General Accessory Type (300A,0423) - DICOM VR: CS
    
    Specifies the type of accessory.
    """
    GRATICULE = "GRATICULE"           # Accessory tray with a radio-opaque grid
    IMAGE_DETECTOR = "IMAGE_DETECTOR" # Image acquisition device positioned in the beam line
    RETICLE = "RETICLE"               # Accessory tray with radio-transparent markers or grid


class BlockType(Enum):
    """Block Type (300A,00F8) - DICOM VR: CS
    
    Type of block.
    """
    SHIELDING = "SHIELDING" # Blocking material is inside contour
    APERTURE = "APERTURE"   # Blocking material is outside contour


class BlockDivergence(Enum):
    """Block Divergence (300A,00FA) - DICOM VR: CS
    
    Indicates presence or otherwise of geometrical divergence.
    """
    PRESENT = "PRESENT" # Block edges are shaped for beam divergence
    ABSENT = "ABSENT"   # Block edges are not shaped for beam divergence


class BlockMountingPosition(Enum):
    """Block Mounting Position (300A,00FB) - DICOM VR: CS
    
    Indicates on which side of the Block Tray the block is mounted.
    """
    PATIENT_SIDE = "PATIENT_SIDE" # Block mounted on side towards patient
    SOURCE_SIDE = "SOURCE_SIDE"   # Block mounted on side towards radiation source


class FluenceDataSource(Enum):
    """Fluence Data Source (3002,0041) - DICOM VR: CS
    
    Source of fluence data.
    """
    CALCULATED = "CALCULATED" # Calculated by a workstation
    MEASURED = "MEASURED"     # Measured by exposure to a film or detector


class SpatialTransformOfDose(Enum):
    """Spatial Transform of Dose (3004,0005) - DICOM VR: CS
    
    The use of transformation in the calculation of the combined dose.
    """
    NONE = "NONE"           # No transformation
    RIGID = "RIGID"         # Only Rigid transform used
    NON_RIGID = "NON_RIGID" # Any other transform used


class DoseSummationType(Enum):
    """Dose Summation Type (3004,000A) - DICOM VR: CS
    
    Type of dose summation.
    """
    PLAN = "PLAN"                         # Dose for entire delivery of all fraction groups
    MULTI_PLAN = "MULTI_PLAN"             # Dose for entire delivery of 2 or more RT Plans
    PLAN_OVERVIEW = "PLAN_OVERVIEW"       # Dose with respect to plan overview parameters
    FRACTION = "FRACTION"                 # Dose for entire delivery of single Fraction Group
    BEAM = "BEAM"                         # Dose for entire delivery of one or more Beams
    BRACHY = "BRACHY"                     # Dose for entire delivery of Brachy Application Setups
    FRACTION_SESSION = "FRACTION_SESSION" # Dose for single session of single Fraction Group
    BEAM_SESSION = "BEAM_SESSION"         # Dose for single session of one or more Beams
    BRACHY_SESSION = "BRACHY_SESSION"     # Dose for single session of Brachy Application Setups
    CONTROL_POINT = "CONTROL_POINT"       # Dose for one or more Control Points within Beam
    RECORD = "RECORD"                     # Dose for RT Beams Treatment Record


class TissueHeterogeneityCorrection(Enum):
    """Tissue Heterogeneity Correction (3004,0014) - DICOM VR: CS
    
    Patient heterogeneity characteristics used for calculating dose.
    """
    IMAGE = "IMAGE"             # Image data
    ROI_OVERRIDE = "ROI_OVERRIDE" # ROI densities override image or water values
    WATER = "WATER"             # Entire volume treated as water equivalent


class DVHType(Enum):
    """DVH Type (3004,0001) - DICOM VR: CS
    
    Type of DVH.
    """
    DIFFERENTIAL = "DIFFERENTIAL" # Differential dose-volume histogram
    CUMULATIVE = "CUMULATIVE"     # Cumulative dose-volume histogram
    NATURAL = "NATURAL"           # Natural dose volume histogram


class DVHROIContributionType(Enum):
    """DVH ROI Contribution Type (3004,0062) - DICOM VR: CS
    
    Specifies whether volume within ROI is included or excluded in DVH.
    """
    INCLUDED = "INCLUDED" # Volume within ROI is included in DVH
    EXCLUDED = "EXCLUDED" # Volume within ROI is excluded from DVH


class DVHVolumeUnits(Enum):
    """DVH Volume Units (3004,0054) - DICOM VR: CS
    
    Volume axis units.
    """
    CM3 = "CM3"       # Cubic centimeters
    PERCENT = "PERCENT" # Percent
    PER_U = "PER_U"   # Volume per u with u(dose)=dose^-3/2


class ROIGenerationAlgorithm(Enum):
    """ROI Generation Algorithm (3006,0036) - DICOM VR: CS
    
    Type of algorithm used to generate ROI.
    """
    AUTOMATIC = "AUTOMATIC"       # Calculated ROI
    SEMIAUTOMATIC = "SEMIAUTOMATIC" # ROI calculated with user assistance
    MANUAL = "MANUAL"             # User-entered ROI


class PlanIntent(Enum):
    """Plan Intent (300A,000A) - DICOM VR: CS
    
    Intent of this plan.
    """
    CURATIVE = "CURATIVE"         # Curative therapy on patient
    PALLIATIVE = "PALLIATIVE"     # Palliative therapy on patient
    PROPHYLACTIC = "PROPHYLACTIC" # Preventative therapy on patient
    VERIFICATION = "VERIFICATION" # Verification of patient plan using phantom
    MACHINE_QA = "MACHINE_QA"     # Quality assurance of delivery machine
    RESEARCH = "RESEARCH"         # Research project
    SERVICE = "SERVICE"           # Machine repair or maintenance operation


class RTplanGeometry(Enum):
    """RT Plan Geometry (300A,000C) - DICOM VR: CS
    
    Describes whether RT Plan is based on patient or treatment device geometry.
    """
    PATIENT = "PATIENT"                   # RT Structure Set exists
    TREATMENT_DEVICE = "TREATMENT_DEVICE" # RT Structure Set does not exist


class RTPlanRelationship(Enum):
    """RT Plan Relationship (300A,0055) - DICOM VR: CS
    
    Relationship of referenced plan with respect to current plan.
    """
    PRIOR = "PRIOR"                   # Plan delivered prior to current treatment
    ALTERNATIVE = "ALTERNATIVE"       # Alternative plan prepared for current treatment
    PREDECESSOR = "PREDECESSOR"       # Plan used in derivation of current plan
    VERIFIED_PLAN = "VERIFIED_PLAN"   # Plan that is verified using the current plan
    CONCURRENT = "CONCURRENT"         # Plan that forms part of set applied in parallel


class RTImageTypeValue3(Enum):
    """RT Image Type Value 3 (0008,0008) - DICOM VR: CS
    
    Defined Terms for Value 3 of Image Type for RT Images.
    """
    DRR = "DRR"           # Digitally reconstructed radiograph
    PORTAL = "PORTAL"     # Digital portal image or portal film image
    SIMULATOR = "SIMULATOR" # Conventional simulator image
    RADIOGRAPH = "RADIOGRAPH" # Radiographic image
    BLANK = "BLANK"       # Image pixels set to background value
    FLUENCE = "FLUENCE"   # Fluence map


class EnhancedRTBeamLimitingDeviceDefinitionFlag(Enum):
    """Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) - DICOM VR: CS

    Whether RT Beam Limiting Devices are specified by Enhanced RT Beam Limiting Device Sequence.
    """
    YES = "YES" # RT Beam Limiting Devices specified by Enhanced sequence
    NO = "NO"   # RT Beam Limiting Devices not specified by Enhanced sequence


class ContourGeometricType(Enum):
    """Contour Geometric Type (3006,0042) - DICOM VR: CS

    Geometric type of contour.
    """
    POINT = "POINT"                         # Single point
    OPEN_PLANAR = "OPEN_PLANAR"             # Open contour containing coplanar points
    OPEN_NONPLANAR = "OPEN_NONPLANAR"       # Open contour containing non-coplanar points
    CLOSED_PLANAR = "CLOSED_PLANAR"         # Closed contour (polygon) containing coplanar points
    CLOSEDPLANAR_XOR = "CLOSEDPLANAR_XOR"   # Closed contour combined using XOR operator


class RTROIInterpretedType(Enum):
    """RT ROI Interpreted Type (3006,00A4) - DICOM VR: CS

    Type of ROI.
    """
    EXTERNAL = "EXTERNAL"                   # External patient contour
    PTV = "PTV"                             # Planning Target Volume
    CTV = "CTV"                             # Clinical Target Volume
    GTV = "GTV"                             # Gross Tumor Volume
    TREATED_VOLUME = "TREATED_VOLUME"       # Treated Volume
    IRRAD_VOLUME = "IRRAD_VOLUME"           # Irradiated Volume
    OAR = "OAR"                             # Organ at Risk
    BOLUS = "BOLUS"                         # Patient bolus for external beam therapy
    AVOIDANCE = "AVOIDANCE"                 # Region in which dose is to be minimized
    ORGAN = "ORGAN"                         # Patient organ
    MARKER = "MARKER"                       # Patient marker or marker on localizer
    REGISTRATION = "REGISTRATION"           # Registration ROI
    ISOCENTER = "ISOCENTER"                 # Treatment isocenter for external beam therapy
    CONTRAST_AGENT = "CONTRAST_AGENT"       # Volume with contrast agent injection
    CAVITY = "CAVITY"                       # Patient anatomical cavity
    BRACHY_CHANNEL = "BRACHY_CHANNEL"       # Brachytherapy channel
    BRACHY_ACCESSORY = "BRACHY_ACCESSORY"   # Brachytherapy accessory device
    BRACHY_SRC_APP = "BRACHY_SRC_APP"       # Brachytherapy source applicator
    BRACHY_CHNL_SHLD = "BRACHY_CHNL_SHLD"   # Brachytherapy channel shield
    SUPPORT = "SUPPORT"                     # External patient support device
    FIXATION = "FIXATION"                   # External patient fixation or immobilization device
    DOSE_REGION = "DOSE_REGION"             # ROI to be used as dose reference
    CONTROL = "CONTROL"                     # ROI for dose optimization and calculation control
    DOSE_MEASUREMENT = "DOSE_MEASUREMENT"   # ROI representing dose measurement device
    DEVICE = "DEVICE"                       # Device not addressed by another defined term


class RTROIRelationship(Enum):
    """RT ROI Relationship (3006,0033) - DICOM VR: CS

    Relationship of referenced ROI with respect to referencing ROI.
    """
    SAME = "SAME"           # ROIs represent the same entity
    ENCLOSED = "ENCLOSED"   # Referenced ROI completely encloses referencing ROI
    ENCLOSING = "ENCLOSING" # Referencing ROI completely encloses referenced ROI


class ROIPhysicalProperty(Enum):
    """ROI Physical Property (3006,00B2) - DICOM VR: CS

    Physical property specified by ROI Physical Property Value.
    """
    REL_MASS_DENSITY = "REL_MASS_DENSITY"   # Mass density relative to water
    REL_ELEC_DENSITY = "REL_ELEC_DENSITY"   # Electron density relative to water
    EFFECTIVE_Z = "EFFECTIVE_Z"             # Effective atomic number
    EFF_Z_PER_A = "EFF_Z_PER_A"             # Ratio of effective atomic number to mass (AMU^-1)
    REL_STOP_RATIO = "REL_STOP_RATIO"       # Ratio of linear stopping power relative to water
    ELEM_FRACTION = "ELEM_FRACTION"         # Elemental composition of the material
    MEAN_EXCI_ENERGY = "MEAN_EXCI_ENERGY"   # Mean Excitation Energy of the material (eV)


class DoseReferenceStructureType(Enum):
    """Dose Reference Structure Type (300A,0014) - DICOM VR: CS

    Structure type of Dose Reference.
    """
    POINT = "POINT"             # Dose reference point specified as ROI
    VOLUME = "VOLUME"           # Dose reference volume specified as ROI
    COORDINATES = "COORDINATES" # Point specified by coordinates
    SITE = "SITE"               # Dose reference clinical site


class DoseReferenceType(Enum):
    """Dose Reference Type (300A,0020) - DICOM VR: CS

    Type of Dose Reference.
    """
    TARGET = "TARGET"           # Treatment target (GTV, PTV, or CTV)
    ORGAN_AT_RISK = "ORGAN_AT_RISK" # Organ at Risk


class DoseValuePurpose(Enum):
    """Dose Value Purpose (300A,061D) - DICOM VR: CS

    Purpose(s) for which dose values are provided.
    """
    TRACKING = "TRACKING"   # Dose values used for tracking
    QA = "QA"               # Dose values used for quality assurance


class DoseValueInterpretation(Enum):
    """Dose Value Interpretation (300A,068B) - DICOM VR: CS

    Interpretation of the dose values.
    """
    NOMINAL = "NOMINAL"     # Nominal dose values
    ACTUAL = "ACTUAL"       # Actual dose values


class FixationDeviceType(Enum):
    """Fixation Device Type (300A,0192) - DICOM VR: CS

    Type of Fixation Device used during Patient Setup.
    """
    BITEBLOCK = "BITEBLOCK"             # Bite block fixation device
    HEADFRAME = "HEADFRAME"             # Head frame fixation device
    MASK = "MASK"                       # Mask fixation device
    MOLD = "MOLD"                       # Mold fixation device
    CAST = "CAST"                       # Cast fixation device
    HEADREST = "HEADREST"               # Head rest fixation device
    BREAST_BOARD = "BREAST_BOARD"       # Breast board fixation device
    BODY_FRAME = "BODY_FRAME"           # Body frame fixation device
    VACUUM_MOLD = "VACUUM_MOLD"         # Vacuum mold fixation device
    WHOLE_BODY_POD = "WHOLE_BODY_POD"   # Whole body pod fixation device
    RECTAL_BALLOON = "RECTAL_BALLOON"   # Rectal balloon fixation device


class ShieldingDeviceType(Enum):
    """Shielding Device Type (300A,01A2) - DICOM VR: CS

    Type of Shielding Device used in Patient Setup.
    """
    GUM = "GUM"         # Gum shield
    EYE = "EYE"         # Eye shield
    GONAD = "GONAD"     # Gonad shield


class SetupTechnique(Enum):
    """Setup Technique (300A,01B0) - DICOM VR: CS

    Setup Technique used in Patient Setup.
    """
    ISOCENTRIC = "ISOCENTRIC"           # Isocentric setup technique
    FIXED_SSD = "FIXED_SSD"             # Fixed source-to-surface distance
    TBI = "TBI"                         # Total body irradiation
    BREAST_BRIDGE = "BREAST_BRIDGE"     # Breast bridge technique
    SKIN_APPOSITION = "SKIN_APPOSITION" # Skin apposition technique


class SetupDeviceType(Enum):
    """Setup Device Type (300A,01B6) - DICOM VR: CS

    Type of Setup Device used for patient alignment.
    """
    LASER_POINTER = "LASER_POINTER"     # Laser pointer alignment device
    DISTANCE_METER = "DISTANCE_METER"   # Distance measurement device
    TABLE_HEIGHT = "TABLE_HEIGHT"       # Table height adjustment device
    MECHANICAL_PTR = "MECHANICAL_PTR"   # Mechanical pointer device
    ARC = "ARC"                         # Arc alignment device


class RespiratoryMotionCompensationTechnique(Enum):
    """Respiratory Motion Compensation Technique (0018,9170) - DICOM VR: CS

    Technique applied to reduce respiratory motion artifacts.
    """
    NONE = "NONE"                       # No respiratory motion compensation
    BREATH_HOLD = "BREATH_HOLD"         # Breath hold technique
    REALTIME = "REALTIME"               # Real-time image acquisition
    GATING = "GATING"                   # Prospective gating
    TRACKING = "TRACKING"               # Prospective motion tracking
    PHASE_ORDERING = "PHASE_ORDERING"   # Prospective phase ordering
    PHASE_RESCANNING = "PHASE_RESCANNING" # Prospective phase rescanning
    RETROSPECTIVE = "RETROSPECTIVE"     # Retrospective gating
    CORRECTION = "CORRECTION"           # Retrospective image correction
    UNKNOWN = "UNKNOWN"                 # Technique not known


class RespiratorySignalSource(Enum):
    """Respiratory Signal Source (0018,9171) - DICOM VR: CS

    Signal source from which respiratory motion is derived.
    """
    NONE = "NONE"                       # No respiratory signal source
    BELT = "BELT"                       # Belt-based signal
    NASAL_PROBE = "NASAL_PROBE"         # Nasal probe signal
    CO2_SENSOR = "CO2_SENSOR"           # CO2 sensor signal
    NAVIGATOR = "NAVIGATOR"             # MR navigator signal
    MR_PHASE = "MR_PHASE"               # MR phase signal
    ECG = "ECG"                         # ECG baseline signal
    SPIROMETER = "SPIROMETER"           # Spirometer signal
    EXTERNAL_MARKER = "EXTERNAL_MARKER" # External motion marker
    INTERNAL_MARKER = "INTERNAL_MARKER" # Internal motion marker
    IMAGE = "IMAGE"                     # Image-derived signal
    UNKNOWN = "UNKNOWN"                 # Signal source not known


class BeamDoseMeaning(Enum):
    """Beam Dose Meaning (300A,008B) - DICOM VR: CS

    Indicates the meaning of Beam Dose.
    """
    BEAM_LEVEL = "BEAM_LEVEL"           # Beam Dose value is individually calculated for this Beam
    FRACTION_LEVEL = "FRACTION_LEVEL"   # Dose is calculated on the Fraction level


class DoseCalibrationConditionsVerifiedFlag(Enum):
    """Dose Calibration Conditions Verified Flag (300C,0123) - DICOM VR: CS

    Indicates whether verifiable calibration conditions were used during treatment planning.
    """
    YES = "YES"     # Calibration conditions were verified
    NO = "NO"       # Calibration conditions were not verified
