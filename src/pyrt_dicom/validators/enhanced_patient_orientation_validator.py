"""Enhanced Patient Orientation Module DICOM validation - PS3.3 C.7.6.30"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class EnhancedPatientOrientationValidator:
    """Validator for DICOM Enhanced Patient Orientation Module (PS3.3 C.7.6.30)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Enhanced Patient Orientation Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = EnhancedPatientOrientationValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = EnhancedPatientOrientationValidator._validate_sequence_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = EnhancedPatientOrientationValidator._validate_enumerated_values(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 required elements."""
        
        # Patient Orientation Code Sequence (0054,0410) - Type 1
        if not hasattr(dataset, 'PatientOrientationCodeSequence') or not dataset.PatientOrientationCodeSequence:
            result["errors"].append("Patient Orientation Code Sequence (0054,0410) is required (Type 1)")
        
        # Patient Orientation Modifier Code Sequence (0054,0412) - Type 1
        if not hasattr(dataset, 'PatientOrientationModifierCodeSequence') or not dataset.PatientOrientationModifierCodeSequence:
            result["errors"].append("Patient Orientation Modifier Code Sequence (0054,0412) is required (Type 1)")
        
        # Patient Equipment Relationship Code Sequence (3010,0030) - Type 1
        if not hasattr(dataset, 'PatientEquipmentRelationshipCodeSequence') or not dataset.PatientEquipmentRelationshipCodeSequence:
            result["errors"].append("Patient Equipment Relationship Code Sequence (3010,0030) is required (Type 1)")
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Validate Patient Orientation Code Sequence structure
        orientation_seq = getattr(dataset, 'PatientOrientationCodeSequence', [])
        for i, item in enumerate(orientation_seq):
            if not item.get('CodeValue'):
                result["errors"].append(
                    f"Patient Orientation Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result["errors"].append(
                    f"Patient Orientation Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result["errors"].append(
                    f"Patient Orientation Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        # Validate Patient Orientation Modifier Code Sequence structure
        modifier_seq = getattr(dataset, 'PatientOrientationModifierCodeSequence', [])
        for i, item in enumerate(modifier_seq):
            if not item.get('CodeValue'):
                result["errors"].append(
                    f"Patient Orientation Modifier Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result["errors"].append(
                    f"Patient Orientation Modifier Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result["errors"].append(
                    f"Patient Orientation Modifier Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        # Validate Patient Equipment Relationship Code Sequence structure
        equipment_seq = getattr(dataset, 'PatientEquipmentRelationshipCodeSequence', [])
        for i, item in enumerate(equipment_seq):
            if not item.get('CodeValue'):
                result["errors"].append(
                    f"Patient Equipment Relationship Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result["errors"].append(
                    f"Patient Equipment Relationship Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result["errors"].append(
                    f"Patient Equipment Relationship Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Common orientation code values for validation
        common_orientation_codes = [
            "102538003",  # recumbent (SCT)
            "C86043",     # erect (NCIt)
            "102539006"   # semi-erect (SCT)
        ]
        
        common_modifier_codes = [
            "40199007",   # supine (SCT)
            "1240000",    # prone (SCT)
            "10904000",   # standing (SCT)
            "33586001"    # sitting (SCT)
        ]
        
        common_equipment_codes = [
            "102540008",  # headfirst (SCT)
            "126833"      # anterior first (DCM)
        ]
        
        # Validate Patient Orientation Code Sequence values
        orientation_seq = getattr(dataset, 'PatientOrientationCodeSequence', [])
        for i, item in enumerate(orientation_seq):
            code_value = item.get('CodeValue', '')
            if code_value and code_value not in common_orientation_codes:
                result["warnings"].append(
                    f"Patient Orientation Code Sequence item {i}: Code Value '{code_value}' "
                    f"is not a commonly used orientation code"
                )
        
        # Validate Patient Orientation Modifier Code Sequence values
        modifier_seq = getattr(dataset, 'PatientOrientationModifierCodeSequence', [])
        for i, item in enumerate(modifier_seq):
            code_value = item.get('CodeValue', '')
            if code_value and code_value not in common_modifier_codes:
                result["warnings"].append(
                    f"Patient Orientation Modifier Code Sequence item {i}: Code Value '{code_value}' "
                    f"is not a commonly used modifier code"
                )
        
        # Validate Patient Equipment Relationship Code Sequence values
        equipment_seq = getattr(dataset, 'PatientEquipmentRelationshipCodeSequence', [])
        for i, item in enumerate(equipment_seq):
            code_value = item.get('CodeValue', '')
            if code_value and code_value not in common_equipment_codes:
                result["warnings"].append(
                    f"Patient Equipment Relationship Code Sequence item {i}: Code Value '{code_value}' "
                    f"is not a commonly used equipment relationship code"
                )
        
        return result
