"""RT Prescription Module DICOM validation - PS3.3 C.8.8.10"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation


class RTPrescriptionValidator:
    """Validator for DICOM RT Prescription Module (PS3.3 C.8.8.10)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Prescription Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = RTPrescriptionValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTPrescriptionValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTPrescriptionValidator._validate_sequence_requirements(dataset, result)
        
        # Validate dose value consistency
        result = RTPrescriptionValidator._validate_dose_value_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            structure_type = dose_ref_item.get('DoseReferenceStructureType', '')
            
            # Type 1C: Referenced ROI Number required if structure type is POINT or VOLUME
            if structure_type in ['POINT', 'VOLUME']:
                if not dose_ref_item.get('ReferencedROINumber'):
                    result["errors"].append(
                        f"Dose Reference Sequence item {i}: "
                        f"Referenced ROI Number (3006,0084) is required when "
                        f"Dose Reference Structure Type is {structure_type}"
                    )
            
            # Type 1C: Dose Reference Point Coordinates required if structure type is COORDINATES
            if structure_type == 'COORDINATES':
                if not dose_ref_item.get('DoseReferencePointCoordinates'):
                    result["errors"].append(
                        f"Dose Reference Sequence item {i}: "
                        "Dose Reference Point Coordinates (300A,0018) is required when "
                        "Dose Reference Structure Type is COORDINATES"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Dose Reference Structure Type (300A,0014)
            structure_type = dose_ref_item.get('DoseReferenceStructureType', '')
            if structure_type:
                valid_structure_types = [stype.value for stype in DoseReferenceStructureType]
                BaseValidator.validate_enumerated_value(
                    structure_type, valid_structure_types,
                    f"Dose Reference Structure Type (300A,0014) in item {i}", result
                )
            
            # Dose Reference Type (300A,0020)
            reference_type = dose_ref_item.get('DoseReferenceType', '')
            if reference_type:
                valid_reference_types = [rtype.value for rtype in DoseReferenceType]
                BaseValidator.validate_enumerated_value(
                    reference_type, valid_reference_types,
                    f"Dose Reference Type (300A,0020) in item {i}", result
                )
            
            # Dose Value Purpose (300A,061D)
            value_purpose = dose_ref_item.get('DoseValuePurpose', '')
            if value_purpose:
                valid_purposes = [purpose.value for purpose in DoseValuePurpose]
                BaseValidator.validate_enumerated_value(
                    value_purpose, valid_purposes,
                    f"Dose Value Purpose (300A,061D) in item {i}", result
                )
            
            # Dose Value Interpretation (300A,068B)
            value_interpretation = dose_ref_item.get('DoseValueInterpretation', '')
            if value_interpretation:
                valid_interpretations = [interp.value for interp in DoseValueInterpretation]
                BaseValidator.validate_enumerated_value(
                    value_interpretation, valid_interpretations,
                    f"Dose Value Interpretation (300A,068B) in item {i}", result
                )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Dose Reference Number is Type 1
            if not dose_ref_item.get('DoseReferenceNumber'):
                result["errors"].append(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Number (300A,0012) is required"
                )
            
            # Dose Reference Structure Type is Type 1
            if not dose_ref_item.get('DoseReferenceStructureType'):
                result["errors"].append(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Structure Type (300A,0014) is required"
                )
            
            # Dose Reference Type is Type 1
            if not dose_ref_item.get('DoseReferenceType'):
                result["errors"].append(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Type (300A,0020) is required"
                )
            
            # Validate Dose Reference Point Coordinates format if present
            coordinates = dose_ref_item.get('DoseReferencePointCoordinates', [])
            if coordinates and len(coordinates) != 3:
                result["errors"].append(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Point Coordinates (300A,0018) must contain exactly 3 values (x,y,z)"
                )

            # Validate Dose Reference UID format if present
            dose_ref_uid = dose_ref_item.get('DoseReferenceUID', '')
            if dose_ref_uid:
                # Basic UID validation - should contain only digits and dots
                if not all(c.isdigit() or c == '.' for c in dose_ref_uid):
                    result["warnings"].append(
                        f"Dose Reference Sequence item {i}: "
                        f"Dose Reference UID ({dose_ref_uid}) should contain only digits and dots"
                    )
                # UID should not start or end with a dot
                if dose_ref_uid.startswith('.') or dose_ref_uid.endswith('.'):
                    result["warnings"].append(
                        f"Dose Reference Sequence item {i}: "
                        f"Dose Reference UID ({dose_ref_uid}) should not start or end with a dot"
                    )

        # Validate uniqueness of dose reference numbers
        dose_ref_numbers = []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            dose_ref_number = dose_ref_item.get('DoseReferenceNumber')
            if dose_ref_number is not None:
                if dose_ref_number in dose_ref_numbers:
                    result["errors"].append(
                        f"Dose Reference Sequence item {i}: "
                        f"Dose Reference Number ({dose_ref_number}) must be unique within the RT Plan"
                    )
                else:
                    dose_ref_numbers.append(dose_ref_number)

        return result
    
    @staticmethod
    def _validate_dose_value_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate dose value consistency and logical constraints."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            reference_type = dose_ref_item.get('DoseReferenceType', '')
            
            # Validate target dose values
            if reference_type == 'TARGET':
                target_min = dose_ref_item.get('TargetMinimumDose')
                target_prescription = dose_ref_item.get('TargetPrescriptionDose')
                target_max = dose_ref_item.get('TargetMaximumDose')
                
                # Check dose ordering: min <= prescription <= max
                if target_min is not None and target_prescription is not None:
                    if target_min > target_prescription:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose should not exceed Target Prescription Dose"
                        )
                
                if target_prescription is not None and target_max is not None:
                    if target_prescription > target_max:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            "Target Prescription Dose should not exceed Target Maximum Dose"
                        )
                
                if target_min is not None and target_max is not None:
                    if target_min > target_max:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose should not exceed Target Maximum Dose"
                        )
                
                # Validate underdose volume fraction
                underdose_fraction = dose_ref_item.get('TargetUnderdoseVolumeFraction')
                if underdose_fraction is not None:
                    if underdose_fraction < 0.0 or underdose_fraction > 100.0:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            f"Target Underdose Volume Fraction ({underdose_fraction}) should be between 0.0 and 100.0 percent"
                        )
                else:
                    # Per DICOM spec: "If the Target Underdose Volume Fraction (300A,0028) is not present, it shall be interpreted as zero."
                    # This is informational, not an error
                    pass
            
            # Validate organ at risk dose values
            elif reference_type == 'ORGAN_AT_RISK':
                oar_full_volume = dose_ref_item.get('OrganAtRiskFullvolumeDose')
                oar_limit = dose_ref_item.get('OrganAtRiskLimitDose')
                oar_maximum = dose_ref_item.get('OrganAtRiskMaximumDose')
                
                # Check dose ordering for OAR
                if oar_full_volume is not None and oar_limit is not None:
                    if oar_full_volume > oar_limit:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose should not exceed Limit Dose"
                        )
                
                if oar_full_volume is not None and oar_maximum is not None:
                    if oar_full_volume > oar_maximum:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose should not exceed Maximum Dose"
                        )
                
                # Validate overdose volume fraction
                overdose_fraction = dose_ref_item.get('OrganAtRiskOverdoseVolumeFraction')
                if overdose_fraction is not None:
                    if overdose_fraction < 0.0 or overdose_fraction > 100.0:
                        result["warnings"].append(
                            f"Dose Reference Sequence item {i}: "
                            f"Organ At Risk Overdose Volume Fraction ({overdose_fraction}) should be between 0.0 and 100.0 percent"
                        )
                else:
                    # Per DICOM spec: "If the Organ at Risk Overdose Volume Fraction (300A,002D) is not present, it shall be interpreted as zero."
                    # This is informational, not an error
                    pass
            
            # Validate general dose values
            delivery_warning = dose_ref_item.get('DeliveryWarningDose')
            delivery_maximum = dose_ref_item.get('DeliveryMaximumDose')
            
            if delivery_warning is not None and delivery_maximum is not None:
                if delivery_warning > delivery_maximum:
                    result["warnings"].append(
                        f"Dose Reference Sequence item {i}: "
                        "Delivery Warning Dose should not exceed Delivery Maximum Dose"
                    )
            
            # Validate constraint weight
            constraint_weight = dose_ref_item.get('ConstraintWeight')
            if constraint_weight is not None and constraint_weight < 0:
                result["warnings"].append(
                    f"Dose Reference Sequence item {i}: "
                    f"Constraint Weight ({constraint_weight}) should be non-negative"
                )
            
            # Validate nominal prior dose
            nominal_prior = dose_ref_item.get('NominalPriorDose')
            if nominal_prior is not None and nominal_prior < 0:
                result["warnings"].append(
                    f"Dose Reference Sequence item {i}: "
                    f"Nominal Prior Dose ({nominal_prior}) should be non-negative"
                )
        
        return result
