"""Image Plane Module DICOM validation - PS3.3 C.7.6.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ImagePlaneValidator:
    """Validator for DICOM Image Plane Module (PS3.3 C.7.6.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Image Plane Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = ImagePlaneValidator._validate_type1_requirements(dataset, result)
        
        # Validate geometric constraints
        if config.validate_conditional_requirements:
            result = ImagePlaneValidator._validate_geometric_constraints(dataset, result)
        
        # Validate value ranges and formats
        result = ImagePlaneValidator._validate_value_constraints(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 (required) attributes."""
        
        # Pixel Spacing (0028,0030) Type 1
        if not hasattr(dataset, 'PixelSpacing'):
            result["errors"].append("Pixel Spacing (0028,0030) is required (Type 1)")
        else:
            if not isinstance(dataset.PixelSpacing, (list, tuple)) or len(dataset.PixelSpacing) != 2:
                result["errors"].append(
                    "Pixel Spacing (0028,0030) must be a pair of values [row_spacing, column_spacing]"
                )
            else:
                try:
                    row_spacing, col_spacing = float(dataset.PixelSpacing[0]), float(dataset.PixelSpacing[1])
                    if row_spacing <= 0 or col_spacing <= 0:
                        result["errors"].append(
                            "Pixel Spacing (0028,0030) values must be positive"
                        )
                except (ValueError, TypeError):
                    result["errors"].append(
                        "Pixel Spacing (0028,0030) values must be numeric"
                    )
        
        # Image Orientation (Patient) (0020,0037) Type 1
        if not hasattr(dataset, 'ImageOrientationPatient'):
            result["errors"].append("Image Orientation (Patient) (0020,0037) is required (Type 1)")
        else:
            if not isinstance(dataset.ImageOrientationPatient, (list, tuple)) or len(dataset.ImageOrientationPatient) != 6:
                result["errors"].append(
                    "Image Orientation (Patient) (0020,0037) must contain 6 direction cosine values"
                )
            else:
                try:
                    cosines = [float(x) for x in dataset.ImageOrientationPatient]
                except (ValueError, TypeError):
                    result["errors"].append(
                        "Image Orientation (Patient) (0020,0037) values must be numeric"
                    )
        
        # Image Position (Patient) (0020,0032) Type 1
        if not hasattr(dataset, 'ImagePositionPatient'):
            result["errors"].append("Image Position (Patient) (0020,0032) is required (Type 1)")
        else:
            if not isinstance(dataset.ImagePositionPatient, (list, tuple)) or len(dataset.ImagePositionPatient) != 3:
                result["errors"].append(
                    "Image Position (Patient) (0020,0032) must contain 3 coordinate values [x, y, z]"
                )
            else:
                try:
                    coords = [float(x) for x in dataset.ImagePositionPatient]
                except (ValueError, TypeError):
                    result["errors"].append(
                        "Image Position (Patient) (0020,0032) values must be numeric"
                    )
        
        return result
    
    @staticmethod
    def _validate_geometric_constraints(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate geometric constraints for direction cosines."""
        
        if not hasattr(dataset, 'ImageOrientationPatient'):
            return result
        
        try:
            cosines = [float(x) for x in dataset.ImageOrientationPatient]
            if len(cosines) != 6:
                return result
            
            row_cosines = cosines[:3]
            col_cosines = cosines[3:]
            
            # Check orthogonality: dot product should be zero
            dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
            if abs(dot_product) > 1e-6:
                result["errors"].append(
                    f"Image Orientation (Patient) (0020,0037) row and column direction cosines "
                    f"must be orthogonal (dot product = {dot_product:.6f}, should be ~0)"
                )
            
            # Check normalization: magnitude should be 1.0
            row_magnitude = sum(r * r for r in row_cosines) ** 0.5
            col_magnitude = sum(c * c for c in col_cosines) ** 0.5
            
            if abs(row_magnitude - 1.0) > 1e-6:
                result["errors"].append(
                    f"Image Orientation (Patient) (0020,0037) row direction cosines "
                    f"must be normalized (magnitude = {row_magnitude:.6f}, should be 1.0)"
                )
            
            if abs(col_magnitude - 1.0) > 1e-6:
                result["errors"].append(
                    f"Image Orientation (Patient) (0020,0037) column direction cosines "
                    f"must be normalized (magnitude = {col_magnitude:.6f}, should be 1.0)"
                )
        
        except (ValueError, TypeError):
            # Numeric validation already handled in _validate_type1_requirements
            pass
        
        return result
    
    @staticmethod
    def _validate_value_constraints(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate value constraints and ranges."""
        
        # Slice Thickness validation
        if hasattr(dataset, 'SliceThickness'):
            try:
                thickness = float(dataset.SliceThickness)
                if thickness <= 0:
                    result["warnings"].append(
                        "Slice Thickness (0018,0050) should be positive"
                    )
            except (ValueError, TypeError):
                result["errors"].append(
                    "Slice Thickness (0018,0050) must be numeric"
                )
        
        # Spacing Between Slices validation
        if hasattr(dataset, 'SpacingBetweenSlices'):
            try:
                spacing = float(dataset.SpacingBetweenSlices)
                if spacing < 0:
                    result["warnings"].append(
                        "Spacing Between Slices (0018,0088) should not be negative "
                        "unless specialized IOD defines the meaning of the sign"
                    )
            except (ValueError, TypeError):
                result["errors"].append(
                    "Spacing Between Slices (0018,0088) must be numeric"
                )
        
        # Slice Location validation
        if hasattr(dataset, 'SliceLocation'):
            try:
                location = float(dataset.SliceLocation)
                # No specific constraints on slice location value
            except (ValueError, TypeError):
                result["errors"].append(
                    "Slice Location (0020,1041) must be numeric"
                )
        
        # Cross-validation: Spacing Between Slices vs Slice Thickness
        if hasattr(dataset, 'SpacingBetweenSlices') and hasattr(dataset, 'SliceThickness'):
            try:
                spacing = float(dataset.SpacingBetweenSlices)
                thickness = float(dataset.SliceThickness)
                
                if spacing > 0 and thickness > 0:
                    if spacing < thickness:
                        result["warnings"].append(
                            f"Spacing Between Slices ({spacing}mm) is less than Slice Thickness ({thickness}mm). "
                            "This may indicate overlapping slices."
                        )
                    elif spacing > thickness * 2:
                        result["warnings"].append(
                            f"Spacing Between Slices ({spacing}mm) is much larger than Slice Thickness ({thickness}mm). "
                            "This may indicate gaps between slices."
                        )
            except (ValueError, TypeError):
                # Numeric validation already handled above
                pass
        
        return result
