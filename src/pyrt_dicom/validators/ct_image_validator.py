"""CT Image Module Validator - DICOM PS3.3 C.8.2.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..enums.contrast_ct_enums import (
    MultiEnergyCTAcquisition, RotationDirection, ExposureModulationType,
    CTImageTypeValue1, CTImageTypeValue2, CTImageTypeValue3, CTImageTypeValue4, CTSamplesPerPixel, CTBitsAllocated,
    CTBitsStored, RescaleType
)
from ..enums.image_enums import PhotometricInterpretation


class CTImageValidator(BaseValidator):
    """Validator for CT Image Module (C.8.2.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate CT Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = CTImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = CTImageValidator._validate_enumerated_values(dataset, result)
        
        # Validate Type 1 required elements
        result = CTImageValidator._validate_required_elements(dataset, result)
        
        # Validate pixel data consistency
        result = CTImageValidator._validate_pixel_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Rescale Type requirement
        multi_energy_acquisition = getattr(dataset, 'MultiEnergyCTAcquisition', '')
        rescale_type = getattr(dataset, 'RescaleType', '')
        
        # Required if Multi-energy CT Acquisition is YES or Rescale Type is not HU
        if (multi_energy_acquisition == "YES" or 
            (rescale_type and rescale_type != "HU")):
            if not hasattr(dataset, 'RescaleType') or not rescale_type:
                result["errors"].append(
                    "Rescale Type (0028,1054) is required when Multi-energy CT Acquisition "
                    "is YES or when Rescale Type is not HU (Hounsfield Units)"
                )
        
        # Type 1C: Energy Weighting Factor requirement
        derivation_code_seq = getattr(dataset, 'DerivationCodeSequence', [])
        has_multi_energy_weighting = False
        
        if derivation_code_seq:
            for item in derivation_code_seq:
                if (item.get('CodeValue') == '113097' and 
                    item.get('CodingSchemeDesignator') == 'DCM'):
                    has_multi_energy_weighting = True
                    break
        
        if has_multi_energy_weighting:
            if not hasattr(dataset, 'EnergyWeightingFactor'):
                result["errors"].append(
                    "Energy Weighting Factor (0018,9353) is required when "
                    "Derivation Code Sequence contains Multi-energy proportional weighting"
                )
        
        # Type 1C: Water Equivalent Diameter Calculation Method requirement
        if hasattr(dataset, 'WaterEquivalentDiameter'):
            if not hasattr(dataset, 'WaterEquivalentDiameterCalculationMethodCodeSequence'):
                result["errors"].append(
                    "Water Equivalent Diameter Calculation Method Code Sequence (0018,1272) "
                    "is required when Water Equivalent Diameter (0018,1271) is present"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM standard."""
        
        # Multi-energy CT Acquisition (0018,9361)
        multi_energy = getattr(dataset, 'MultiEnergyCTAcquisition', '')
        if multi_energy:
            valid_values = [acquisition.value for acquisition in MultiEnergyCTAcquisition]
            BaseValidator.validate_enumerated_value(
                multi_energy, valid_values,
                "Multi-energy CT Acquisition (0018,9361)", result
            )
        
        # Samples per Pixel (0028,0002) - must be 1 for CT
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None and samples_per_pixel != CTSamplesPerPixel.ONE.value:
            result["errors"].append(
                f"Samples per Pixel (0028,0002) must be {CTSamplesPerPixel.ONE.value} for CT images, got {samples_per_pixel}"
            )
        
        # Photometric Interpretation (0028,0004)
        photometric = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric:
            valid_values = [PhotometricInterpretation.MONOCHROME1.value, PhotometricInterpretation.MONOCHROME2.value]
            BaseValidator.validate_enumerated_value(
                photometric, valid_values,
                "Photometric Interpretation (0028,0004)", result
            )
        
        # Bits Allocated (0028,0100) - must be 16 for CT
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        if bits_allocated is not None and bits_allocated != CTBitsAllocated.SIXTEEN.value:
            result["errors"].append(
                f"Bits Allocated (0028,0100) must be {CTBitsAllocated.SIXTEEN.value} for CT images, got {bits_allocated}"
            )
        
        # Bits Stored (0028,0101) - valid values for CT
        bits_stored = getattr(dataset, 'BitsStored', None)
        if bits_stored is not None:
            valid_values = [str(bits.value) for bits in CTBitsStored]
            BaseValidator.validate_enumerated_value(
                str(bits_stored), valid_values,
                "Bits Stored (0028,0101)", result
            )
        
        # Rotation Direction (0018,1140)
        rotation_direction = getattr(dataset, 'RotationDirection', '')
        if rotation_direction:
            valid_values = [direction.value for direction in RotationDirection]
            BaseValidator.validate_enumerated_value(
                rotation_direction, valid_values,
                "Rotation Direction (0018,1140)", result
            )
        
        # Exposure Modulation Type (0018,9323)
        exposure_modulation = getattr(dataset, 'ExposureModulationType', '')
        if exposure_modulation:
            valid_values = [modulation.value for modulation in ExposureModulationType]
            BaseValidator.validate_enumerated_value(
                exposure_modulation, valid_values,
                "Exposure Modulation Type (0018,9323)", result
            )
        
        # Image Type (0008,0008) validation
        image_type = getattr(dataset, 'ImageType', [])
        if image_type:
            # Map of value positions to their corresponding enums and descriptions
            value_validations = [
                (CTImageTypeValue1, "Value 1"),
                (CTImageTypeValue2, "Value 2"),
                (CTImageTypeValue3, "Value 3"),
            ]

            # Validate values 1-3
            for i, (enum_class, description) in enumerate(value_validations):
                if len(image_type) > i:
                    valid_values = [value.value for value in enum_class]
                    BaseValidator.validate_enumerated_value(
                        image_type[i], valid_values,
                        f"Image Type (0008,0008) {description}", result
                    )

            # Special handling for Value 4 (multi-energy CT)
            if multi_energy == "YES":
                if len(image_type) >= 4:
                    valid_values = [value.value for value in CTImageTypeValue4]
                    BaseValidator.validate_enumerated_value(
                        image_type[3], valid_values,
                        "Image Type (0008,0008) Value 4 for Multi-energy CT", result
                    )
                else:
                    result["errors"].append(
                        "Image Type (0008,0008) Value 4 shall be present if Multi-energy CT Acquisition (0018,9361) has a Value of YES"
                    )
            elif multi_energy == "NO":
                if len(image_type) >= 4:
                    result["errors"].append(
                        "Image Type (0008,0008) Value 4 shall NOT be present if Multi-energy CT Acquisition (0018,9361) has a Value of NO"
                    )
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate presence of Type 1 required elements."""
        
        required_elements = [
            ('ImageType', 'Image Type (0008,0008)'),
            ('SamplesPerPixel', 'Samples per Pixel (0028,0002)'),
            ('PhotometricInterpretation', 'Photometric Interpretation (0028,0004)'),
            ('BitsAllocated', 'Bits Allocated (0028,0100)'),
            ('BitsStored', 'Bits Stored (0028,0101)'),
            ('HighBit', 'High Bit (0028,0102)'),
            ('RescaleIntercept', 'Rescale Intercept (0028,1052)'),
            ('RescaleSlope', 'Rescale Slope (0028,1053)')
        ]
        
        for attr_name, display_name in required_elements:
            if not hasattr(dataset, attr_name):
                result["errors"].append(f"{display_name} is required (Type 1)")
        
        return result
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate consistency between pixel data attributes."""
        
        # High Bit should be one less than Bits Stored
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_stored is not None and high_bit is not None:
            expected_high_bit = bits_stored - 1
            if high_bit != expected_high_bit:
                result["errors"].append(
                    f"High Bit (0028,0102) should be one less than Bits Stored (0028,0101). "
                    f"Expected {expected_high_bit}, got {high_bit}"
                )
        
        # Validate Image Type Value 4 presence for multi-energy
        multi_energy = getattr(dataset, 'MultiEnergyCTAcquisition', '')
        image_type = getattr(dataset, 'ImageType', [])
        
        if multi_energy == "YES" and len(image_type) < 4:
            result["errors"].append(
                "Image Type (0008,0008) Value 4 shall be present if "
                "Multi-energy CT Acquisition (0018,9361) has a Value of YES"
            )
        
        # Validate CT Additional X-Ray Source Sequence constraints
        multi_energy_seq = getattr(dataset, 'MultiEnergyCTAcquisitionSequence', [])
        additional_source_seq = getattr(dataset, 'CTAdditionalXRaySourceSequence', [])
        
        if multi_energy == "YES" and additional_source_seq:
            result["warnings"].append(
                "CT Additional X-Ray Source Sequence (0018,9360) shall not be present "
                "if Multi-energy CT Acquisition (0018,9361) is YES. "
                "Use Multi-energy CT Acquisition Sequence (0018,9362) instead"
            )
        
        return result
