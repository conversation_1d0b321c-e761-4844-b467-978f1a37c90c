"""General Equipment Module DICOM validation - PS3.3 C.7.5.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class GeneralEquipmentValidator:
    """Validator for DICOM General Equipment Module (PS3.3 C.7.5.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate General Equipment Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = GeneralEquipmentValidator._validate_conditional_requirements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = GeneralEquipmentValidator._validate_sequence_requirements(dataset, result)
        
        # Validate calibration date/time pairing
        result = GeneralEquipmentValidator._validate_calibration_pairing(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Pixel Padding Value required if Pixel Padding Range Limit is present 
        # and either Pixel Data or Pixel Data Provider URL is present
        has_pixel_padding_range_limit = hasattr(dataset, 'PixelPaddingRangeLimit')
        has_pixel_data = hasattr(dataset, 'PixelData') or hasattr(dataset, 'PixelDataProviderURL')
        
        if has_pixel_padding_range_limit and has_pixel_data:
            if not hasattr(dataset, 'PixelPaddingValue'):
                result["errors"].append(
                    "Pixel Padding Value (0028,0120) is required when Pixel Padding Range Limit (0028,0121) "
                    "is present and either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present"
                )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Institutional Department Type Code Sequence - each item needs Code Sequence Macro attributes
        dept_type_seq = getattr(dataset, 'InstitutionalDepartmentTypeCodeSequence', [])
        for i, item in enumerate(dept_type_seq):
            if not item.get('CodeValue'):
                result["errors"].append(
                    f"Institutional Department Type Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result["errors"].append(
                    f"Institutional Department Type Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result["errors"].append(
                    f"Institutional Department Type Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        # UDI Sequence - each item needs UDI Macro attributes
        udi_seq = getattr(dataset, 'UDISequence', [])
        for i, item in enumerate(udi_seq):
            if not item.get('UniqueDeviceIdentifier'):
                result["errors"].append(
                    f"UDI Sequence item {i}: Unique Device Identifier is required"
                )
        
        return result
    
    @staticmethod
    def _validate_calibration_pairing(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate calibration date/time pairing requirements."""
        
        # Date of Last Calibration and Time of Last Calibration should be paired
        date_calibration = getattr(dataset, 'DateOfLastCalibration', [])
        time_calibration = getattr(dataset, 'TimeOfLastCalibration', [])
        
        # Time of Last Calibration has no meaning unless Date of Last Calibration is also present
        if time_calibration and not date_calibration:
            result["warnings"].append(
                "Time of Last Calibration (0018,1201) has no meaning unless "
                "Date of Last Calibration (0018,1200) is also present"
            )
        
        # When both are present, they should have the same number of values (paired)
        if date_calibration and time_calibration:
            if len(date_calibration) != len(time_calibration):
                result["warnings"].append(
                    "Date of Last Calibration (0018,1200) and Time of Last Calibration (0018,1201) "
                    "should have the same number of values when both are present (paired)"
                )
        
        return result
