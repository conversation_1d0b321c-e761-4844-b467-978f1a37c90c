"""RT Patient Setup Module DICOM validation - PS3.3 C.8.8.12"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..enums.rt_enums import (
    FixationDeviceType, ShieldingDeviceType, SetupTechnique, SetupDeviceType,
    RespiratoryMotionCompensationTechnique, RespiratorySignalSource
)
from ..enums.series_enums import PatientPosition


class RTPatientSetupValidator:
    """Validator for DICOM RT Patient Setup Module (PS3.3 C.8.8.12)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Patient Setup Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = RTPatientSetupValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTPatientSetupValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTPatientSetupValidator._validate_sequence_requirements(dataset, result)
        
        # Validate setup parameter consistency
        result = RTPatientSetupValidator._validate_setup_parameter_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        patient_setup_seq = getattr(dataset, 'PatientSetupSequence', [])
        for i, setup_item in enumerate(patient_setup_seq):
            # Type 1C: Either Patient Position or Patient Additional Position must be present
            has_patient_position = 'PatientPosition' in setup_item
            has_additional_position = 'PatientAdditionalPosition' in setup_item
            
            if not has_patient_position and not has_additional_position:
                result["errors"].append(
                    f"Patient Setup Sequence item {i}: "
                    "Either Patient Position (0018,5100) or Patient Additional Position (300A,0184) must be present"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        patient_setup_seq = getattr(dataset, 'PatientSetupSequence', [])
        for i, setup_item in enumerate(patient_setup_seq):
            # Patient Position (0018,5100)
            patient_position = setup_item.get('PatientPosition', '')
            if patient_position:
                valid_positions = [pos.value for pos in PatientPosition]
                BaseValidator.validate_enumerated_value(
                    patient_position, valid_positions,
                    f"Patient Position (0018,5100) in Setup {i}", result
                )
            
            # Setup Technique (300A,01B0)
            setup_technique = setup_item.get('SetupTechnique', '')
            if setup_technique:
                valid_techniques = [tech.value for tech in SetupTechnique]
                BaseValidator.validate_enumerated_value(
                    setup_technique, valid_techniques,
                    f"Setup Technique (300A,01B0) in Setup {i}", result
                )
            
            # Validate Fixation Device Sequence enumerated values
            fixation_device_seq = setup_item.get('FixationDeviceSequence', [])
            for j, fixation_item in enumerate(fixation_device_seq):
                device_type = fixation_item.get('FixationDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in FixationDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f"Fixation Device Type (300A,0192) in Device {j}, Setup {i}", result
                    )
            
            # Validate Shielding Device Sequence enumerated values
            shielding_device_seq = setup_item.get('ShieldingDeviceSequence', [])
            for j, shielding_item in enumerate(shielding_device_seq):
                device_type = shielding_item.get('ShieldingDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in ShieldingDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f"Shielding Device Type (300A,01A2) in Device {j}, Setup {i}", result
                    )
            
            # Validate Setup Device Sequence enumerated values
            setup_device_seq = setup_item.get('SetupDeviceSequence', [])
            for j, device_item in enumerate(setup_device_seq):
                device_type = device_item.get('SetupDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in SetupDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f"Setup Device Type (300A,01B6) in Device {j}, Setup {i}", result
                    )
            
            # Validate Motion Synchronization Sequence enumerated values
            motion_sync_seq = setup_item.get('MotionSynchronizationSequence', [])
            for j, motion_item in enumerate(motion_sync_seq):
                compensation_technique = motion_item.get('RespiratoryMotionCompensationTechnique', '')
                if compensation_technique:
                    valid_techniques = [tech.value for tech in RespiratoryMotionCompensationTechnique]
                    BaseValidator.validate_enumerated_value(
                        compensation_technique, valid_techniques,
                        f"Respiratory Motion Compensation Technique (0018,9170) in Motion {j}, Setup {i}", result
                    )
                
                signal_source = motion_item.get('RespiratorySignalSource', '')
                if signal_source:
                    valid_sources = [source.value for source in RespiratorySignalSource]
                    BaseValidator.validate_enumerated_value(
                        signal_source, valid_sources,
                        f"Respiratory Signal Source (0018,9171) in Motion {j}, Setup {i}", result
                    )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Patient Setup Sequence validation (Type 1)
        patient_setup_seq = getattr(dataset, 'PatientSetupSequence', [])
        if not patient_setup_seq:
            result["errors"].append(
                "Patient Setup Sequence (300A,0180) is required (Type 1)"
            )
        
        for i, setup_item in enumerate(patient_setup_seq):
            # Patient Setup Number is Type 1
            if not setup_item.get('PatientSetupNumber'):
                result["errors"].append(
                    f"Patient Setup Sequence item {i}: "
                    "Patient Setup Number (300A,0182) is required"
                )
            
            # Validate Fixation Device Sequence
            fixation_device_seq = setup_item.get('FixationDeviceSequence', [])
            for j, fixation_item in enumerate(fixation_device_seq):
                if not fixation_item.get('FixationDeviceType'):
                    result["errors"].append(
                        f"Fixation Device Sequence item {j} in Setup {i}: "
                        "Fixation Device Type (300A,0192) is required"
                    )
                if 'FixationDeviceLabel' not in fixation_item:
                    result["errors"].append(
                        f"Fixation Device Sequence item {j} in Setup {i}: "
                        "Fixation Device Label (300A,0194) is required (Type 2)"
                    )
            
            # Validate Shielding Device Sequence
            shielding_device_seq = setup_item.get('ShieldingDeviceSequence', [])
            for j, shielding_item in enumerate(shielding_device_seq):
                if not shielding_item.get('ShieldingDeviceType'):
                    result["errors"].append(
                        f"Shielding Device Sequence item {j} in Setup {i}: "
                        "Shielding Device Type (300A,01A2) is required"
                    )
                if 'ShieldingDeviceLabel' not in shielding_item:
                    result["errors"].append(
                        f"Shielding Device Sequence item {j} in Setup {i}: "
                        "Shielding Device Label (300A,01A4) is required (Type 2)"
                    )
            
            # Validate Setup Device Sequence
            setup_device_seq = setup_item.get('SetupDeviceSequence', [])
            for j, device_item in enumerate(setup_device_seq):
                if not device_item.get('SetupDeviceType'):
                    result["errors"].append(
                        f"Setup Device Sequence item {j} in Setup {i}: "
                        "Setup Device Type (300A,01B6) is required"
                    )
                if 'SetupDeviceLabel' not in device_item:
                    result["errors"].append(
                        f"Setup Device Sequence item {j} in Setup {i}: "
                        "Setup Device Label (300A,01B8) is required (Type 2)"
                    )
                if 'SetupDeviceParameter' not in device_item:
                    result["errors"].append(
                        f"Setup Device Sequence item {j} in Setup {i}: "
                        "Setup Device Parameter (300A,01BC) is required (Type 2)"
                    )
            
            # Validate Motion Synchronization Sequence
            motion_sync_seq = setup_item.get('MotionSynchronizationSequence', [])
            for j, motion_item in enumerate(motion_sync_seq):
                if not motion_item.get('RespiratoryMotionCompensationTechnique'):
                    result["errors"].append(
                        f"Motion Synchronization Sequence item {j} in Setup {i}: "
                        "Respiratory Motion Compensation Technique (0018,9170) is required"
                    )
                if not motion_item.get('RespiratorySignalSource'):
                    result["errors"].append(
                        f"Motion Synchronization Sequence item {j} in Setup {i}: "
                        "Respiratory Signal Source (0018,9171) is required"
                    )
            
            # Validate Referenced Setup Image Sequence
            ref_setup_image_seq = setup_item.get('ReferencedSetupImageSequence', [])
            for j, image_item in enumerate(ref_setup_image_seq):
                if not image_item.get('ReferencedSOPClassUID'):
                    result["errors"].append(
                        f"Referenced Setup Image Sequence item {j} in Setup {i}: "
                        "Referenced SOP Class UID (0008,1150) is required"
                    )
                if not image_item.get('ReferencedSOPInstanceUID'):
                    result["errors"].append(
                        f"Referenced Setup Image Sequence item {j} in Setup {i}: "
                        "Referenced SOP Instance UID (0008,1155) is required"
                    )

            # Validate Patient Treatment Preparation Sequence
            patient_prep_seq = setup_item.get('PatientTreatmentPreparationSequence', [])
            for j, prep_item in enumerate(patient_prep_seq):
                prep_procedure_seq = prep_item.get('PatientTreatmentPreparationProcedureCodeSequence', [])
                if not prep_procedure_seq:
                    result["errors"].append(
                        f"Patient Treatment Preparation Sequence item {j} in Setup {i}: "
                        "Patient Treatment Preparation Procedure Code Sequence (0018,002A) is required"
                    )

                # Validate code sequence items
                for k, code_item in enumerate(prep_procedure_seq):
                    if not code_item.get('CodeValue'):
                        result["errors"].append(
                            f"Patient Treatment Preparation Procedure Code Sequence item {k} in Prep {j}, Setup {i}: "
                            "Code Value (0008,0100) is required"
                        )
                    if not code_item.get('CodingSchemeDesignator'):
                        result["errors"].append(
                            f"Patient Treatment Preparation Procedure Code Sequence item {k} in Prep {j}, Setup {i}: "
                            "Coding Scheme Designator (0008,0102) is required"
                        )
                    if not code_item.get('CodeMeaning'):
                        result["errors"].append(
                            f"Patient Treatment Preparation Procedure Code Sequence item {k} in Prep {j}, Setup {i}: "
                            "Code Meaning (0008,0104) is required"
                        )

        # Validate uniqueness of patient setup numbers
        setup_numbers = []
        for i, setup_item in enumerate(patient_setup_seq):
            setup_number = setup_item.get('PatientSetupNumber')
            if setup_number is not None:
                if setup_number in setup_numbers:
                    result["errors"].append(
                        f"Patient Setup Sequence item {i}: "
                        f"Patient Setup Number ({setup_number}) must be unique within the RT Plan"
                    )
                else:
                    setup_numbers.append(setup_number)
        
        return result
    
    @staticmethod
    def _validate_setup_parameter_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate setup parameter consistency and logical constraints."""
        
        patient_setup_seq = getattr(dataset, 'PatientSetupSequence', [])
        for i, setup_item in enumerate(patient_setup_seq):
            # Validate fixation device angles
            fixation_device_seq = setup_item.get('FixationDeviceSequence', [])
            for j, fixation_item in enumerate(fixation_device_seq):
                pitch_angle = fixation_item.get('FixationDevicePitchAngle')
                roll_angle = fixation_item.get('FixationDeviceRollAngle')
                
                if pitch_angle is not None and (pitch_angle < -180 or pitch_angle > 180):
                    result["warnings"].append(
                        f"Fixation Device Sequence item {j} in Setup {i}: "
                        f"Fixation Device Pitch Angle ({pitch_angle}) should be between -180 and 180 degrees"
                    )
                
                if roll_angle is not None and (roll_angle < -180 or roll_angle > 180):
                    result["warnings"].append(
                        f"Fixation Device Sequence item {j} in Setup {i}: "
                        f"Fixation Device Roll Angle ({roll_angle}) should be between -180 and 180 degrees"
                    )
            
            # Validate table top displacement values are reasonable
            displacement_fields = [
                ('TableTopVerticalSetupDisplacement', '300A,01D2'),
                ('TableTopLongitudinalSetupDisplacement', '300A,01D4'),
                ('TableTopLateralSetupDisplacement', '300A,01D6')
            ]
            
            for field_name, tag in displacement_fields:
                displacement = setup_item.get(field_name)
                if displacement is not None:
                    if abs(displacement) > 1000:  # More than 1 meter seems excessive
                        result["warnings"].append(
                            f"Patient Setup Sequence item {i}: "
                            f"{field_name} ({tag}) value ({displacement} mm) seems unusually large"
                        )

            # Validate respiratory motion compensation consistency
            motion_sync_seq = setup_item.get('MotionSynchronizationSequence', [])
            for j, motion_item in enumerate(motion_sync_seq):
                compensation_technique = motion_item.get('RespiratoryMotionCompensationTechnique', '')
                signal_source = motion_item.get('RespiratorySignalSource', '')

                # Validate logical consistency between technique and source
                if compensation_technique == 'NONE' and signal_source != 'NONE':
                    result["warnings"].append(
                        f"Motion Synchronization Sequence item {j} in Setup {i}: "
                        "Respiratory Signal Source should be NONE when compensation technique is NONE"
                    )
                elif compensation_technique != 'NONE' and signal_source == 'NONE':
                    result["warnings"].append(
                        f"Motion Synchronization Sequence item {j} in Setup {i}: "
                        "Respiratory Signal Source should not be NONE when compensation technique is specified"
                    )

                # Validate technique-specific signal source compatibility
                if compensation_technique == 'BREATH_HOLD' and signal_source not in ['BELT', 'SPIROMETER', 'EXTERNAL_MARKER']:
                    result["warnings"].append(
                        f"Motion Synchronization Sequence item {j} in Setup {i}: "
                        f"Signal source {signal_source} may not be optimal for breath hold technique"
                    )

        return result
