"""General Series Module DICOM validation - PS3.3 C.7.3.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class GeneralSeriesValidator:
    """Validator for DICOM General Series Module (PS3.3 C.7.3.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate General Series Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Check Type 1 requirements
        if not hasattr(dataset, 'Modality') or not dataset.Modality:
            result["errors"].append("Modality (0008,0060) is required (Type 1)")
        
        if not hasattr(dataset, 'SeriesInstanceUID') or not dataset.SeriesInstanceUID:
            result["errors"].append("Series Instance UID (0020,000E) is required (Type 1)")
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = GeneralSeriesValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = GeneralSeriesValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = GeneralSeriesValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 2C: Laterality required for paired body parts if Image/Frame/Measurement Laterality not present
        # This is a complex check that would require knowledge of the body part and other modules
        # For now, we'll just check if Laterality is present when it might be needed
        body_part = getattr(dataset, 'BodyPartExamined', '')
        paired_body_parts = ['BREAST', 'EYE', 'KIDNEY', 'LUNG', 'OVARY', 'TESTIS', 'ARM', 'LEG', 'HAND', 'FOOT']
        
        if any(part.lower() in body_part.lower() for part in paired_body_parts):
            if not hasattr(dataset, 'Laterality'):
                result["warnings"].append(
                    "Laterality (0020,0060) may be required for paired body part examination "
                    "if Image Laterality, Frame Laterality, or Measurement Laterality are not present"
                )
        
        # Type 2C: Patient Position required for specific SOP Classes
        sop_class_uid = getattr(dataset, 'SOPClassUID', '')
        required_sop_classes = [
            "1.2.840.10008.5.1.4.1.1.2",    # CT Image Storage
            "1.2.840.10008.5.1.4.1.1.4",    # MR Image Storage
            "1.2.840.10008.5.1.4.1.1.2.1",  # Enhanced CT Image Storage
            "1.2.840.10008.5.1.4.1.1.4.1",  # Enhanced MR Image Storage
            "1.2.840.10008.5.1.4.1.1.4.3",  # Enhanced Color MR Image Storage
            "1.2.840.10008.5.1.4.1.1.4.2"   # MR Spectroscopy Storage
        ]
        
        if sop_class_uid in required_sop_classes:
            has_patient_position = hasattr(dataset, 'PatientPosition')
            has_patient_orientation = hasattr(dataset, 'PatientOrientationCodeSequence')
            
            if not has_patient_position and not has_patient_orientation:
                result["errors"].append(
                    "Patient Position (0018,5100) is required for this SOP Class "
                    "when Patient Orientation Code Sequence (0054,0410) is not present"
                )
        
        # Type 1C: Anatomical Orientation Type required for non-human organisms with non-bipedal frame of reference
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or 
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))
        
        if is_non_human and not hasattr(dataset, 'AnatomicalOrientationType'):
            result["warnings"].append(
                "Anatomical Orientation Type (0010,2210) may be required for non-human organisms "
                "with non-bipedal anatomical frame of reference"
            )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Laterality (0020,0060)
        laterality = getattr(dataset, 'Laterality', '')
        if laterality:
            BaseValidator.validate_enumerated_value(
                laterality, ["R", "L"],
                "Laterality (0020,0060)", result
            )
        
        # Anatomical Orientation Type (0010,2210)
        orientation_type = getattr(dataset, 'AnatomicalOrientationType', '')
        if orientation_type:
            BaseValidator.validate_enumerated_value(
                orientation_type, ["BIPED", "QUADRUPED"],
                "Anatomical Orientation Type (0010,2210)", result
            )
        
        # Patient Position (0018,5100) - extensive list of valid values
        patient_position = getattr(dataset, 'PatientPosition', '')
        if patient_position:
            valid_positions = [
                "HFP", "HFS", "HFDR", "HFDL", "HFV", "HFI",
                "FFDR", "FFDL", "FFP", "FFS", "FFV", "FFI",
                "LFP", "LFS", "LFDR", "LFDL",
                "RFP", "RFS", "RFDR", "RFDL",
                "AFP", "AFS", "AFDR", "AFDL",
                "PFP", "PFS", "PFDR", "PFDL"
            ]
            BaseValidator.validate_enumerated_value(
                patient_position, valid_positions,
                "Patient Position (0018,5100)", result
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Performing Physician Identification Sequence validation
        performing_phys_seq = getattr(dataset, 'PerformingPhysicianIdentificationSequence', [])
        performing_phys_name = getattr(dataset, 'PerformingPhysiciansName', '')
        if performing_phys_seq and performing_phys_name:
            # Check that number and order correspond if both are present
            performing_names = performing_phys_name.split('\\') if isinstance(performing_phys_name, str) else []
            if len(performing_phys_seq) != len(performing_names):
                result["warnings"].append(
                    "Performing Physician Identification Sequence (0008,1052): "
                    "Number of items should correspond to Performing Physician's Name (0008,1050)"
                )
        
        # Operator Identification Sequence validation
        operator_seq = getattr(dataset, 'OperatorIdentificationSequence', [])
        operator_name = getattr(dataset, 'OperatorsName', '')
        if operator_seq and operator_name:
            # Check that number and order correspond if both are present
            operator_names = operator_name.split('\\') if isinstance(operator_name, str) else []
            if len(operator_seq) != len(operator_names):
                result["warnings"].append(
                    "Operator Identification Sequence (0008,1072): "
                    "Number of items should correspond to Operators' Name (0008,1070)"
                )
        
        # Referenced Performed Procedure Step Sequence validation
        ref_pps_seq = getattr(dataset, 'ReferencedPerformedProcedureStepSequence', [])
        if len(ref_pps_seq) > 1:
            result["errors"].append(
                "Referenced Performed Procedure Step Sequence (0008,1111): "
                "Only a single Item is permitted in this Sequence"
            )
        
        # Related Series Sequence validation
        related_series_seq = getattr(dataset, 'RelatedSeriesSequence', [])
        for i, item in enumerate(related_series_seq):
            if not item.get('StudyInstanceUID'):
                result["errors"].append(
                    f"Related Series Sequence item {i}: "
                    "Study Instance UID (0020,000D) is required"
                )
            if not item.get('SeriesInstanceUID'):
                result["errors"].append(
                    f"Related Series Sequence item {i}: "
                    "Series Instance UID (0020,000E) is required"
                )
        
        # Series Description Code Sequence validation
        series_desc_seq = getattr(dataset, 'SeriesDescriptionCodeSequence', [])
        if len(series_desc_seq) > 1:
            result["errors"].append(
                "Series Description Code Sequence (0008,103F): "
                "Only a single Item is permitted in this Sequence"
            )
        
        return result
