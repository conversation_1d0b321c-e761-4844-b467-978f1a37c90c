"""RT DVH Module DICOM validation - PS3.3 C.8.8.4"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..enums.dose_enums import DoseUnits, DoseType
from ..enums.rt_enums import DVHType, DVHROIContributionType, DVHVolumeUnits


class RTDVHValidator:
    """Validator for DICOM RT DVH Module (PS3.3 C.8.8.4)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT DVH Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTDVHValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTDVHValidator._validate_sequence_requirements(dataset, result)
        
        # Validate DVH data consistency
        result = RTDVHValidator._validate_dvh_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Validate DVH Sequence enumerated values
        dvh_sequence = getattr(dataset, 'DVHSequence', [])
        for i, dvh_item in enumerate(dvh_sequence):
            # DVH Type (3004,0001)
            dvh_type = dvh_item.get('DVHType', '')
            if dvh_type:
                valid_types = [dtype.value for dtype in DVHType]
                BaseValidator.validate_enumerated_value(
                    dvh_type, valid_types,
                    f"DVH Type (3004,0001) in DVH item {i}", result
                )
            
            # Dose Units (3004,0002)
            dose_units = dvh_item.get('DoseUnits', '')
            if dose_units:
                valid_units = [units.value for units in DoseUnits]
                BaseValidator.validate_enumerated_value(
                    dose_units, valid_units,
                    f"Dose Units (3004,0002) in DVH item {i}", result
                )
            
            # Dose Type (3004,0004)
            dose_type = dvh_item.get('DoseType', '')
            if dose_type:
                valid_dose_types = [dtype.value for dtype in DoseType]
                BaseValidator.validate_enumerated_value(
                    dose_type, valid_dose_types,
                    f"Dose Type (3004,0004) in DVH item {i}", result
                )
            
            # DVH Volume Units (3004,0054)
            dvh_volume_units = dvh_item.get('DVHVolumeUnits', '')
            if dvh_volume_units:
                valid_volume_units = [units.value for units in DVHVolumeUnits]
                BaseValidator.validate_enumerated_value(
                    dvh_volume_units, valid_volume_units,
                    f"DVH Volume Units (3004,0054) in DVH item {i}", result
                )
            
            # DVH ROI Contribution Type in DVH Referenced ROI Sequence
            dvh_roi_seq = dvh_item.get('DVHReferencedROISequence', [])
            for j, roi_item in enumerate(dvh_roi_seq):
                contribution_type = roi_item.get('DVHROIContributionType', '')
                if contribution_type:
                    valid_contribution_types = [ctype.value for ctype in DVHROIContributionType]
                    BaseValidator.validate_enumerated_value(
                        contribution_type, valid_contribution_types,
                        f"DVH ROI Contribution Type (3004,0062) in DVH item {i}, ROI item {j}", result
                    )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Referenced Structure Set Sequence validation (Type 1)
        ref_structure_set_seq = getattr(dataset, 'ReferencedStructureSetSequence', [])
        if not ref_structure_set_seq:
            result["errors"].append(
                "Referenced Structure Set Sequence (300C,0060) is required (Type 1)"
            )
        
        for i, struct_item in enumerate(ref_structure_set_seq):
            if not struct_item.get('ReferencedSOPClassUID'):
                result["errors"].append(
                    f"Referenced Structure Set Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not struct_item.get('ReferencedSOPInstanceUID'):
                result["errors"].append(
                    f"Referenced Structure Set Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # DVH Sequence validation (Type 1)
        dvh_sequence = getattr(dataset, 'DVHSequence', [])
        if not dvh_sequence:
            result["errors"].append(
                "DVH Sequence (3004,0050) is required (Type 1)"
            )
        
        for i, dvh_item in enumerate(dvh_sequence):
            # DVH Referenced ROI Sequence validation (Type 1)
            dvh_roi_seq = dvh_item.get('DVHReferencedROISequence', [])
            if not dvh_roi_seq:
                result["errors"].append(
                    f"DVH Sequence item {i}: "
                    "DVH Referenced ROI Sequence (3004,0060) is required (Type 1)"
                )
            
            for j, roi_item in enumerate(dvh_roi_seq):
                if not roi_item.get('ReferencedROINumber'):
                    result["errors"].append(
                        f"DVH Referenced ROI Sequence item {j} in DVH item {i}: "
                        "Referenced ROI Number (3006,0084) is required"
                    )
                if not roi_item.get('DVHROIContributionType'):
                    result["errors"].append(
                        f"DVH Referenced ROI Sequence item {j} in DVH item {i}: "
                        "DVH ROI Contribution Type (3004,0062) is required"
                    )
            
            # Required DVH elements validation
            required_dvh_elements = [
                ('DVHType', '3004,0001'),
                ('DoseUnits', '3004,0002'),
                ('DoseType', '3004,0004'),
                ('DVHDoseScaling', '3004,0052'),
                ('DVHVolumeUnits', '3004,0054'),
                ('DVHNumberOfBins', '3004,0056'),
                ('DVHData', '3004,0058')
            ]
            
            for element_name, tag in required_dvh_elements:
                if not dvh_item.get(element_name):
                    result["errors"].append(
                        f"DVH Sequence item {i}: "
                        f"{element_name} ({tag}) is required (Type 1)"
                    )
        
        return result
    
    @staticmethod
    def _validate_dvh_data_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate DVH data consistency and logical constraints."""
        
        dvh_sequence = getattr(dataset, 'DVHSequence', [])
        for i, dvh_item in enumerate(dvh_sequence):
            # Validate DVH data length consistency
            dvh_number_of_bins = dvh_item.get('DVHNumberOfBins', 0)
            dvh_data = dvh_item.get('DVHData', [])
            
            if dvh_number_of_bins and dvh_data:
                expected_data_length = dvh_number_of_bins * 2  # Each bin has dose and volume
                if len(dvh_data) != expected_data_length:
                    result["errors"].append(
                        f"DVH Sequence item {i}: "
                        f"DVH Data length ({len(dvh_data)}) should be twice the "
                        f"Number of Bins ({dvh_number_of_bins})"
                    )
            
            # Validate dose scaling is positive
            dvh_dose_scaling = dvh_item.get('DVHDoseScaling', 0)
            if dvh_dose_scaling is not None and dvh_dose_scaling <= 0:
                result["warnings"].append(
                    f"DVH Sequence item {i}: "
                    "DVH Dose Scaling should be positive"
                )
            
            # Validate min/max/mean dose consistency if present
            dvh_min_dose = dvh_item.get('DVHMinimumDose')
            dvh_max_dose = dvh_item.get('DVHMaximumDose')
            dvh_mean_dose = dvh_item.get('DVHMeanDose')
            
            if dvh_min_dose is not None and dvh_max_dose is not None:
                if dvh_min_dose > dvh_max_dose:
                    result["warnings"].append(
                        f"DVH Sequence item {i}: "
                        "DVH Minimum Dose should not exceed DVH Maximum Dose"
                    )
                
                if dvh_mean_dose is not None:
                    if dvh_mean_dose < dvh_min_dose or dvh_mean_dose > dvh_max_dose:
                        result["warnings"].append(
                            f"DVH Sequence item {i}: "
                            "DVH Mean Dose should be between minimum and maximum doses"
                        )
        
        # Validate normalization consistency
        dvh_norm_point = getattr(dataset, 'DVHNormalizationPoint', None)
        dvh_norm_dose = getattr(dataset, 'DVHNormalizationDoseValue', None)
        
        # Check if any DVH uses RELATIVE dose units
        has_relative_dose = any(
            dvh_item.get('DoseUnits', '') == 'RELATIVE' 
            for dvh_item in dvh_sequence
        )
        
        if has_relative_dose and dvh_norm_dose is None:
            result["warnings"].append(
                "DVH Normalization Dose Value (3004,0042) should be present "
                "when any DVH uses RELATIVE dose units"
            )
        
        return result
