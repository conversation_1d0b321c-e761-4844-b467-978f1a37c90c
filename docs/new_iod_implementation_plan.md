# RT IOD Class Design Documentation

## Overview

This document details the design and implementation of Radiotherapy Information Object Definition (IOD) classes built on top of a pydicom-based module system. The design prioritizes ease of use through explicit constructor signatures, comprehensive docstrings, and intelligent IDE support.

## Architecture Principles

### Design Pattern: Explicit Module Constructor
- **Required modules** are enforced through constructor parameters
- **Optional modules** use `Optional[ModuleType] = None` parameters  
- **Conditional modules** are validated at runtime with clear error messages
- **IDE IntelliSense** provides complete guidance through typed parameters and docstrings

### Data Handling: Explicit Dataset Generation with Live References
- **Memory efficient**: Modules are stored by reference, not copied
- **Explicit generation**: DICOM datasets created on-demand via `generate_dataset()` method calls
- **No caching overhead**: Fresh datasets generated each time, reflecting current module state
- **Large data sharing**: 3D arrays (dose, CT images) are shared between IODs and modules

## Base Architecture

### IOD Base Class

```python
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
import pydicom
import numpy as np

class IODValidationError(Exception):
    """Raised when IOD module requirements are not satisfied."""
    pass

class BaseIOD(ABC):
    """Base class for all DICOM Information Object Definitions."""
    
    def __init__(self):
        self._modules: Dict[str, BaseModule] = {}
    
    def generate_dataset(self) -> pydicom.Dataset:
        """Generate a fresh DICOM dataset from current module state.
        
        This method explicitly converts the collection of modules into a pydicom
        Dataset by merging all module data. Each call generates a new dataset
        reflecting the current state of all modules.
        
        Returns:
            Fresh DICOM dataset containing all module data
            
        Example:
            ```python
            # Create IOD with modules
            rt_dose = RTDoseIOD(patient_module, study_module, ...)
            
            # Generate dataset for export
            dataset = rt_dose.generate_dataset()
            dataset.save_as("dose.dcm")
            
            # Modify modules
            rt_dose.get_module('rt_dose').dose_grid_scaling = 0.002
            
            # Generate fresh dataset with updated data
            updated_dataset = rt_dose.generate_dataset()
            updated_dataset.save_as("dose_updated.dcm")
            ```
        """
        ds = pydicom.Dataset()
        for module in self._modules.values():
            ds.update(module.dataset)
        return ds
    
    def generate_file_dataset(self) -> pydicom.FileDataset:
        """Generate a DICOM FileDataset ready for file I/O operations.
        
        This method creates a FileDataset with proper file meta information
        for direct saving to DICOM files. Use this when you need the full
        file dataset structure with DICOM file headers.
        
        Returns:
            FileDataset with complete DICOM file structure
            
        Example:
            ```python
            # Generate file dataset for saving
            file_dataset = rt_dose.generate_file_dataset()
            file_dataset.save_as("dose_with_meta.dcm")
            ```
        """
        ds = self.generate_dataset()
        
        # Create file meta information
        file_meta = pydicom.Dataset()
        file_meta.MediaStorageSOPClassUID = getattr(ds, 'SOPClassUID', '')
        file_meta.MediaStorageSOPInstanceUID = getattr(ds, 'SOPInstanceUID', '')
        file_meta.ImplementationClassUID = "1.2.840.10008.1.2.1"  # Explicit VR Little Endian
        file_meta.TransferSyntaxUID = "1.2.840.10008.1.2.1"
        
        # Create FileDataset
        file_ds = pydicom.FileDataset(
            filename='',
            dataset=ds,
            file_meta=file_meta,
            is_implicit_VR=False,
            is_little_endian=True
        )
        
        return file_ds
    
    def get_module(self, module_name: str) -> Optional['BaseModule']:
        """Get reference to a specific module for direct modification.
        
        Args:
            module_name: Name of module (e.g., 'patient', 'study', 'series')
            
        Returns:
            Live reference to module, or None if not present
            
        Note:
            Modifying returned module will affect subsequent calls to
            generate_dataset() and generate_file_dataset().
        """
        return self._modules.get(module_name)
    
    @abstractmethod
    def _validate_dependencies(self) -> None:
        """Validate IOD-specific module dependencies."""
        pass
```

## RT IOD Class Implementations

### CT IOD

```python
class CTIOD(BaseIOD):
    """CT Information Object Definition for radiotherapy planning.
    
    Combines CT image modules into a complete DICOM CT object suitable for 
    treatment planning workflows. Supports both single-frame and multi-frame CT images.
    """
    
    def __init__(self,
                 # Required modules for all CT images
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 general_series_module: GeneralSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 general_image_module: GeneralImageModule,
                 image_pixel_module: ImagePixelModule,
                 ct_image_module: CTImageModule,
                 # Optional modules
                 contrast_bolus_module: Optional[ContrastBolusModule] = None,
                 device_module: Optional[DeviceModule] = None,
                 intervention_module: Optional[InterventionModule] = None,
                 # Conditional modules (auto-validated)
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 # RT-specific optional modules
                 rt_image_module: Optional[RTImageModule] = None):
        """Create CT IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates
            general_series_module: Series-level metadata (must have modality='CT')
            frame_of_reference_module: Spatial coordinate system reference for treatment planning
            general_equipment_module: CT scanner equipment information and settings
            general_image_module: General image attributes including image type and acquisition info
            image_pixel_module: Pixel data and image matrix information for CT slices
            ct_image_module: CT-specific parameters (kVp, mAs, reconstruction kernel, etc.)
            
            contrast_bolus_module: Optional contrast agent administration details
            device_module: Optional additional device information beyond general equipment
            intervention_module: Optional interventional procedure information
            
            image_plane_module: Optional spatial characteristics (requires multi-slice data)
            overlay_plane_module: Optional overlay graphics and annotations  
            multi_frame_module: Optional multi-frame attributes for enhanced CT
            
            rt_image_module: Optional RT-specific image attributes for RT planning integration
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect
            
        Example:
            ```python
            # Create CT IOD for treatment planning
            ct_iod = CTIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                general_series_module=GeneralSeriesModule.from_required_elements(
                    modality="CT",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Siemens"
                ),
                general_image_module=GeneralImageModule.from_required_elements(
                    image_type=["ORIGINAL", "PRIMARY", "AXIAL"]
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    rows=512, columns=512, pixel_data=ct_pixel_array
                ),
                ct_image_module=CTImageModule.from_required_elements(
                    kvp=120, exposure_time=1000
                ),
                # Optional: Add image plane for spatial information
                image_plane_module=ImagePlaneModule.from_required_elements(
                    pixel_spacing=[0.97656, 0.97656],
                    image_orientation_patient=[1,0,0,0,1,0],
                    image_position_patient=[-250, -250, 100]
                )
            )
            
            # Export to DICOM file
            ct_iod.generate_file_dataset().save_as("ct_slice.dcm")
            ```
        """
        super().__init__()
        
        # Validate CT-specific requirements
        if general_series_module.modality != 'CT':
            raise IODValidationError("general_series_module must have modality='CT'")
        
        # Validate conditional dependencies
        self._validate_dependencies(image_plane_module, overlay_plane_module, 
                                  multi_frame_module, general_image_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'general_series': general_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'general_image': general_image_module,
            'image_pixel': image_pixel_module,
            'ct_image': ct_image_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'contrast_bolus': contrast_bolus_module,
            'device': device_module,
            'intervention': intervention_module,
            'image_plane': image_plane_module,
            'overlay_plane': overlay_plane_module,
            'multi_frame': multi_frame_module,
            'rt_image': rt_image_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, image_plane_module, overlay_plane_module, 
                             multi_frame_module, general_image_module) -> None:
        """Validate CT IOD conditional module dependencies."""
        # Most conditional modules for CT don't have strict dependencies
        # But we can add validation logic here if needed
        pass
    
    @property
    def has_contrast(self) -> bool:
        """Check if contrast agent was used.
        
        Returns:
            True if contrast bolus module is present
        """
        return 'contrast_bolus' in self._modules
    
    @property
    def is_enhanced_ct(self) -> bool:
        """Check if this is an enhanced CT image.
        
        Returns:
            True if multi-frame module is present (indicates enhanced CT)
        """
        return 'multi_frame' in self._modules
    
    @property
    def has_image_plane_data(self) -> bool:
        """Check if spatial positioning data is available.
        
        Returns:
            True if image plane module is present for spatial calculations
        """
        return 'image_plane' in self._modules
    
    def get_ct_parameters(self) -> Dict[str, Any]:
        """Get CT acquisition parameters summary.
        
        Returns:
            Dictionary with key CT imaging parameters
        """
        ct_module = self._modules.get('ct_image')
        if not ct_module:
            return {}
        
        return {
            'kvp': getattr(ct_module, 'kvp', None),
            'exposure_time': getattr(ct_module, 'exposure_time', None),
            'tube_current': getattr(ct_module, 'x_ray_tube_current', None),
            'filter_type': getattr(ct_module, 'filter_type', None),
            'reconstruction_algorithm': getattr(ct_module, 'convolution_kernel', None)
        }
```

### MR IOD

```python
class MRIOD(BaseIOD):
    """MR Information Object Definition for radiotherapy planning.
    
    Combines MR image modules into a complete DICOM MR object suitable for 
    treatment planning workflows. Supports various MR sequences and enhanced imaging.
    """
    
    def __init__(self,
                 # Required modules for all MR images
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 general_series_module: GeneralSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 general_image_module: GeneralImageModule,
                 image_pixel_module: ImagePixelModule,
                 mr_image_module: MRImageModule,
                 # Optional modules
                 contrast_bolus_module: Optional[ContrastBolusModule] = None,
                 device_module: Optional[DeviceModule] = None,
                 # Conditional modules  
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 # MR-specific optional modules
                 enhanced_mr_image_module: Optional[EnhancedMRImageModule] = None,
                 mr_spectroscopy_module: Optional[MRSpectroscopyModule] = None):
        """Create MR IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates
            general_series_module: Series-level metadata (must have modality='MR')
            frame_of_reference_module: Spatial coordinate system reference for treatment planning
            general_equipment_module: MR scanner equipment information and field strength
            general_image_module: General image attributes including image type and acquisition info
            image_pixel_module: Pixel data and image matrix information for MR slices
            mr_image_module: MR-specific parameters (TR, TE, flip angle, sequence name, etc.)
            
            contrast_bolus_module: Optional contrast agent administration details
            device_module: Optional additional device information (coils, etc.)
            
            image_plane_module: Optional spatial characteristics for slice positioning
            overlay_plane_module: Optional overlay graphics and ROI annotations
            multi_frame_module: Optional multi-frame attributes for enhanced MR sequences
            
            enhanced_mr_image_module: Optional enhanced MR specific attributes (advanced sequences)
            mr_spectroscopy_module: Optional MR spectroscopy data and parameters
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect
            
        Example:
            ```python
            # Create MR IOD for T1-weighted planning image
            mr_iod = MRIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                general_series_module=GeneralSeriesModule.from_required_elements(
                    modality="MR",
                    series_instance_uid="*******.6",
                    series_description="T1 MPRAGE"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Siemens",
                    magnetic_field_strength=3.0
                ),
                general_image_module=GeneralImageModule.from_required_elements(
                    image_type=["ORIGINAL", "PRIMARY", "M", "FFE"]
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    rows=256, columns=256, pixel_data=mr_pixel_array
                ),
                mr_image_module=MRImageModule.from_required_elements(
                    repetition_time=2300.0,
                    echo_time=4.6,
                    flip_angle=8.0,
                    scanning_sequence="GR"
                )
            )
            ```
        """
        super().__init__()
        
        # Validate MR-specific requirements
        if general_series_module.modality != 'MR':
            raise IODValidationError("general_series_module must have modality='MR'")
        
        # Validate conditional dependencies
        self._validate_dependencies(enhanced_mr_image_module, multi_frame_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'general_series': general_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'general_image': general_image_module,
            'image_pixel': image_pixel_module,
            'mr_image': mr_image_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'contrast_bolus': contrast_bolus_module,
            'device': device_module,
            'image_plane': image_plane_module,
            'overlay_plane': overlay_plane_module,
            'multi_frame': multi_frame_module,
            'enhanced_mr_image': enhanced_mr_image_module,
            'mr_spectroscopy': mr_spectroscopy_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, enhanced_mr_image_module, multi_frame_module) -> None:
        """Validate MR IOD conditional module dependencies."""
        if enhanced_mr_image_module and not multi_frame_module:
            raise IODValidationError("enhanced_mr_image_module requires multi_frame_module")
    
    @property
    def is_enhanced_mr(self) -> bool:
        """Check if this is an enhanced MR image.
        
        Returns:
            True if enhanced MR image module is present
        """
        return 'enhanced_mr_image' in self._modules
    
    @property
    def has_spectroscopy_data(self) -> bool:
        """Check if MR spectroscopy data is included.
        
        Returns:
            True if MR spectroscopy module is present
        """
        return 'mr_spectroscopy' in self._modules
    
    @property
    def is_multi_frame_mr(self) -> bool:
        """Check if this is a multi-frame MR sequence.
        
        Returns:
            True if multi-frame module is present
        """
        return 'multi_frame' in self._modules
    
    def get_sequence_parameters(self) -> Dict[str, Any]:
        """Get MR sequence parameters summary.
        
        Returns:
            Dictionary with key MR sequence parameters
        """
        mr_module = self._modules.get('mr_image')
        if not mr_module:
            return {}
        
        return {
            'sequence_name': getattr(mr_module, 'sequence_name', None),
            'scanning_sequence': getattr(mr_module, 'scanning_sequence', None),
            'sequence_variant': getattr(mr_module, 'sequence_variant', None),
            'repetition_time': getattr(mr_module, 'repetition_time', None),
            'echo_time': getattr(mr_module, 'echo_time', None),
            'flip_angle': getattr(mr_module, 'flip_angle', None),
            'magnetic_field_strength': getattr(self._modules.get('general_equipment'), 'magnetic_field_strength', None)
        }
```

### RT Plan IOD

```python
class RTPlanIOD(BaseIOD):
    """RT Plan Information Object Definition.
    
    Combines radiotherapy planning modules into a complete DICOM RT Plan object
    containing beam definitions, dose prescriptions, and treatment parameters.
    """
    
    def __init__(self,
                 # Required modules for all RT Plans
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 rt_general_plan_module: RTGeneralPlanModule,
                 # Conditional modules (validated based on plan type)
                 rt_prescription_module: Optional[RTPrescriptionModule] = None,
                 rt_tolerance_tables_module: Optional[RTToleranceTablesModule] = None,
                 rt_patient_setup_module: Optional[RTPatientSetupModule] = None,
                 rt_fraction_scheme_module: Optional[RTFractionSchemeModule] = None,
                 # Treatment technique specific modules
                 rt_beams_module: Optional[RTBeamsModule] = None,
                 rt_brachy_application_setups_module: Optional[RTBrachyApplicationSetupsModule] = None,
                 rt_ion_beams_module: Optional[RTIonBeamsModule] = None,
                 # Approval and QA modules
                 approval_module: Optional[ApprovalModule] = None,
                 audio_module: Optional[AudioModule] = None):
        """Create RT Plan IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates  
            rt_series_module: RT series metadata (must have modality='RTPLAN')
            frame_of_reference_module: Spatial coordinate system reference
            general_equipment_module: Treatment planning system equipment information
            rt_general_plan_module: General plan information (plan name, date, intent, etc.)
            
            rt_prescription_module: Optional dose prescription and target definitions
            rt_tolerance_tables_module: Optional machine tolerance and limit tables
            rt_patient_setup_module: Optional patient positioning and setup instructions
            rt_fraction_scheme_module: Optional fractionation schedule and timing
            
            rt_beams_module: Optional external beam definitions (required for EBRT plans)
            rt_brachy_application_setups_module: Optional brachytherapy setup (required for brachy plans)
            rt_ion_beams_module: Optional ion beam definitions (required for ion therapy)
            
            approval_module: Optional treatment approval information and signatures
            audio_module: Optional audio annotations and instructions
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or plan type requirements not met
            
        Example:
            ```python
            # Create EBRT plan IOD
            rt_plan = RTPlanIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTPLAN",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Varian Medical Systems"
                ),
                rt_general_plan_module=RTGeneralPlanModule.from_required_elements(
                    rt_plan_label="VMAT Plan",
                    rt_plan_date="20240101",
                    rt_plan_intent="CURATIVE"
                ),
                # Required for external beam plans
                rt_beams_module=RTBeamsModule.from_required_elements(
                    beam_sequence=[beam1, beam2, beam3]
                ),
                # Optional prescription information
                rt_prescription_module=RTPrescriptionModule.from_required_elements(
                    dose_reference_sequence=dose_refs
                )
            )
            ```
        """
        super().__init__()
        
        # Validate RT Plan-specific requirements
        if rt_series_module.modality != 'RTPLAN':
            raise IODValidationError("rt_series_module must have modality='RTPLAN'")
        
        # Validate treatment technique dependencies
        self._validate_dependencies(rt_general_plan_module, rt_beams_module, 
                                  rt_brachy_application_setups_module, rt_ion_beams_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'rt_general_plan': rt_general_plan_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'rt_prescription': rt_prescription_module,
            'rt_tolerance_tables': rt_tolerance_tables_module,
            'rt_patient_setup': rt_patient_setup_module,
            'rt_fraction_scheme': rt_fraction_scheme_module,
            'rt_beams': rt_beams_module,
            'rt_brachy_application_setups': rt_brachy_application_setups_module,
            'rt_ion_beams': rt_ion_beams_module,
            'approval': approval_module,
            'audio': audio_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, rt_general_plan_module, rt_beams_module, 
                             rt_brachy_application_setups_module, rt_ion_beams_module) -> None:
        """Validate RT Plan treatment technique dependencies."""
        plan_intent = getattr(rt_general_plan_module, 'rt_plan_intent', None)
        
        # Check that at least one treatment technique module is present for curative plans
        if plan_intent == 'CURATIVE':
            technique_modules = [rt_beams_module, rt_brachy_application_setups_module, rt_ion_beams_module]
            if not any(technique_modules):
                raise IODValidationError(
                    "Curative RT plans require at least one treatment technique module "
                    "(rt_beams_module, rt_brachy_application_setups_module, or rt_ion_beams_module)"
                )
    
    @property
    def is_external_beam_plan(self) -> bool:
        """Check if this is an external beam radiation plan.
        
        Returns:
            True if RT beams module is present
        """
        return 'rt_beams' in self._modules
    
    @property
    def is_brachytherapy_plan(self) -> bool:
        """Check if this is a brachytherapy plan.
        
        Returns:
            True if RT brachy application setups module is present
        """
        return 'rt_brachy_application_setups' in self._modules
    
    @property
    def is_ion_therapy_plan(self) -> bool:
        """Check if this is an ion therapy plan.
        
        Returns:
            True if RT ion beams module is present
        """
        return 'rt_ion_beams' in self._modules
    
    @property
    def has_prescription_data(self) -> bool:
        """Check if dose prescription information is included.
        
        Returns:
            True if RT prescription module is present
        """
        return 'rt_prescription' in self._modules
    
    @property
    def has_fractionation_scheme(self) -> bool:
        """Check if fractionation schedule is defined.
        
        Returns:
            True if RT fraction scheme module is present
        """
        return 'rt_fraction_scheme' in self._modules
    
    def get_plan_summary(self) -> Dict[str, Any]:
        """Get RT plan summary information.
        
        Returns:
            Dictionary with key plan parameters and technique information
        """
        plan_module = self._modules.get('rt_general_plan')
        if not plan_module:
            return {}
        
        # Determine treatment technique
        technique = "Unknown"
        if 'rt_beams' in self._modules:
            technique = "External Beam"
        elif 'rt_brachy_application_setups' in self._modules:
            technique = "Brachytherapy"
        elif 'rt_ion_beams' in self._modules:
            technique = "Ion Therapy"
        
        return {
            'plan_label': getattr(plan_module, 'rt_plan_label', None),
            'plan_date': getattr(plan_module, 'rt_plan_date', None),
            'plan_intent': getattr(plan_module, 'rt_plan_intent', None),
            'treatment_technique': technique,
            'number_of_fractions': getattr(plan_module, 'rt_plan_description', None)
        }
```

### RT Dose IOD

```python
class RTDoseIOD(BaseIOD):
    """RT Dose Information Object Definition.
    
    Combines radiotherapy dose distribution modules into a complete DICOM RT Dose object
    containing 3D dose matrices, scaling factors, and dose calculation parameters.
    """
    
    def __init__(self,
                 # Required modules for all RT Dose objects
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 rt_dose_module: RTDoseModule,
                 # Optional modules for enhanced dose information
                 general_image_module: Optional[GeneralImageModule] = None,
                 rt_dvh_module: Optional[RTDVHModule] = None,
                 # Conditional modules (validated based on image presence)
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 # RT-specific optional modules
                 structure_set_module: Optional[StructureSetModule] = None,
                 approval_module: Optional[ApprovalModule] = None):
        """Create RT Dose IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates
            rt_series_module: RT series metadata (must have modality='RTDOSE')
            frame_of_reference_module: Spatial coordinate system reference matching planning CT
            general_equipment_module: Dose calculation system equipment information
            rt_dose_module: Dose distribution data, scaling factors, and calculation parameters
            
            general_image_module: Optional image attributes for dose visualization and display
            rt_dvh_module: Optional dose volume histogram data for structures
            
            image_plane_module: Optional spatial characteristics for dose grid (requires general_image_module)
            multi_frame_module: Optional multi-frame attributes for dose slices (requires general_image_module)
            overlay_plane_module: Optional overlay graphics for isodose lines (requires general_image_module)
            
            structure_set_module: Optional structure definitions for DVH calculations
            approval_module: Optional dose calculation approval information
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect
            
        Example:
            ```python
            # Create RT Dose IOD with 3D dose distribution
            rt_dose = RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"  # Must match planning CT
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Eclipse Treatment Planning System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units="GY",
                    dose_type="PHYSICAL", 
                    dose_grid_scaling=0.001,  # Scale factor for dose values
                    pixel_data=dose_3d_array  # 3D numpy array with dose distribution
                ),
                # Optional: Add DVH data for analysis
                rt_dvh_module=RTDVHModule.from_required_elements(
                    dvh_sequence=structure_dvhs
                ),
                # Optional: Add image plane for spatial registration
                image_plane_module=ImagePlaneModule.from_required_elements(
                    pixel_spacing=[2.5, 2.5],  # Dose grid resolution
                    image_orientation_patient=[1,0,0,0,1,0],
                    image_position_patient=[-200, -200, -100]  # Dose grid origin
                )
            )
            
            # Access dose statistics
            max_dose = np.max(rt_dose.get_module('rt_dose').pixel_data) * rt_dose.get_module('rt_dose').dose_grid_scaling
            print(f"Maximum dose: {max_dose} Gy")
            ```
        """
        super().__init__()
        
        # Validate RT Dose-specific requirements
        if rt_series_module.modality != 'RTDOSE':
            raise IODValidationError("rt_series_module must have modality='RTDOSE'")
        
        # Validate conditional dependencies
        self._validate_dependencies(image_plane_module, multi_frame_module, 
                                  overlay_plane_module, general_image_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'rt_dose': rt_dose_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'general_image': general_image_module,
            'rt_dvh': rt_dvh_module,
            'image_plane': image_plane_module,
            'multi_frame': multi_frame_module,
            'overlay_plane': overlay_plane_module,
            'structure_set': structure_set_module,
            'approval': approval_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, image_plane_module, multi_frame_module, 
                             overlay_plane_module, general_image_module) -> None:
        """Validate RT Dose IOD conditional module dependencies."""
        if image_plane_module and not general_image_module:
            raise IODValidationError("image_plane_module requires general_image_module")
        if multi_frame_module and not general_image_module:
            raise IODValidationError("multi_frame_module requires general_image_module")
        if overlay_plane_module and not general_image_module:
            raise IODValidationError("overlay_plane_module requires general_image_module")
    
    @property
    def has_dvh_data(self) -> bool:
        """Check if dose volume histogram data is included.
        
        Returns:
            True if RT DVH module is present
        """
        return 'rt_dvh' in self._modules
    
    @property
    def has_image_representation(self) -> bool:
        """Check if dose can be displayed as images.
        
        Returns:
            True if general image module is present for dose visualization
        """
        return 'general_image' in self._modules
    
    @property
    def is_3d_dose(self) -> bool:
        """Check if this contains 3D dose distribution data.
        
        Returns:
            True if pixel data is 3D array
        """
        dose_module = self._modules.get('rt_dose')
        if not dose_module:
            return False
        
        pixel_data = getattr(dose_module, 'pixel_data', None)
        return pixel_data is not None and len(pixel_data.shape) == 3
    
    def get_dose_summary(self) -> Dict[str, Any]:
        """Get dose distribution summary information.
        
        Returns:
            Dictionary with key dose parameters and statistics
        """
        dose_module = self._modules.get('rt_dose')
        if not dose_module:
            return {}
        
        pixel_data = getattr(dose_module, 'pixel_data', None)
        scaling = getattr(dose_module, 'dose_grid_scaling', 1.0)
        
        if pixel_data is not None:
            # Calculate dose statistics
            dose_array = pixel_data * scaling
            return {
                'dose_units': getattr(dose_module, 'dose_units', None),
                'dose_type': getattr(dose_module, 'dose_type', None),
                'dose_summation_type': getattr(dose_module, 'dose_summation_type', None),
                'grid_dimensions': pixel_data.shape,
                'max_dose': float(np.max(dose_array)) if dose_array.size > 0 else 0.0,
                'mean_dose': float(np.mean(dose_array)) if dose_array.size > 0 else 0.0,
                'dose_grid_scaling': scaling
            }
        
        return {
            'dose_units': getattr(dose_module, 'dose_units', None),
            'dose_type': getattr(dose_module, 'dose_type', None),
            'dose_grid_scaling': scaling
        }
```

### RT Image IOD

```python
class RTImageIOD(BaseIOD):
    """RT Image Information Object Definition.
    
    Combines radiotherapy imaging modules into a complete DICOM RT Image object
    for portal imaging, verification imaging, and treatment guidance.
    """
    
    def __init__(self,
                 # Required modules for all RT Images
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 general_image_module: GeneralImageModule,
                 image_pixel_module: ImagePixelModule,
                 rt_image_module: RTImageModule,
                 # Optional modules for enhanced RT imaging
                 device_module: Optional[DeviceModule] = None,
                 general_acquisition_module: Optional[GeneralAcquisitionModule] = None,
                 # Conditional modules (validated based on image type)
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 cine_module: Optional[CineModule] = None,
                 # RT-specific optional modules
                 approval_module: Optional[ApprovalModule] = None):
        """Create RT Image IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates
            rt_series_module: RT series metadata (must have modality='RTIMAGE')
            frame_of_reference_module: Spatial coordinate system reference for treatment geometry
            general_equipment_module: Imaging equipment information (LINAC, portal imager, etc.)
            general_image_module: General image attributes including image type and acquisition info
            image_pixel_module: Pixel data and image matrix information for RT images
            rt_image_module: RT-specific imaging parameters (beam angles, SID, field size, etc.)
            
            device_module: Optional detailed device information for imaging hardware
            general_acquisition_module: Optional acquisition timing and sequence information
            
            image_plane_module: Optional spatial characteristics for image positioning
            overlay_plane_module: Optional overlay graphics for field verification and annotations
            multi_frame_module: Optional multi-frame attributes for cine or time-series imaging
            cine_module: Optional cine playback attributes for dynamic imaging
            
            approval_module: Optional image approval and quality assurance information
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect
            
        Example:
            ```python
            # Create RT Portal Image IOD for treatment verification
            rt_image = RTImageIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTIMAGE",
                    series_instance_uid="*******.6",
                    series_description="Portal Images"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"  # Match treatment planning frame
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Varian Medical Systems",
                    station_name="TrueBeam STx"
                ),
                general_image_module=GeneralImageModule.from_required_elements(
                    image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    rows=1024, columns=768, pixel_data=portal_image_array
                ),
                rt_image_module=RTImageModule.from_required_elements(
                    rt_image_label="Portal Image 1",
                    rt_image_plane="NORMAL",
                    rt_image_position=[100.0, 0.0, 0.0],  # Isocenter position
                    radiation_machine_name="TrueBeam STx",
                    primary_dosimeter_unit="MU",
                    image_plane_pixel_spacing=[0.392, 0.392]  # mm/pixel
                ),
                # Optional: Add image plane for precise positioning
                image_plane_module=ImagePlaneModule.from_required_elements(
                    pixel_spacing=[0.392, 0.392],
                    image_orientation_patient=[1,0,0,0,1,0],
                    image_position_patient=[-150, -100, 0]  # Portal imager position
                )
            )
            
            # Access RT imaging parameters
            rt_params = rt_image.get_rt_imaging_parameters()
            print(f"RT Image: {rt_params['image_label']}, SID: {rt_params['rt_image_sid']} mm")
            ```
        """
        super().__init__()
        
        # Validate RT Image-specific requirements
        if rt_series_module.modality != 'RTIMAGE':
            raise IODValidationError("rt_series_module must have modality='RTIMAGE'")
        
        # Validate conditional dependencies
        self._validate_dependencies(image_plane_module, multi_frame_module, 
                                  cine_module, general_image_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'general_image': general_image_module,
            'image_pixel': image_pixel_module,
            'rt_image': rt_image_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'device': device_module,
            'general_acquisition': general_acquisition_module,
            'image_plane': image_plane_module,
            'overlay_plane': overlay_plane_module,
            'multi_frame': multi_frame_module,
            'cine': cine_module,
            'approval': approval_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, image_plane_module, multi_frame_module, 
                             cine_module, general_image_module) -> None:
        """Validate RT Image IOD conditional module dependencies."""
        if cine_module and not multi_frame_module:
            raise IODValidationError("cine_module requires multi_frame_module")
    
    @property
    def is_portal_image(self) -> bool:
        """Check if this is a portal/verification image.
        
        Returns:
            True if image type indicates portal imaging
        """
        general_image = self._modules.get('general_image')
        if not general_image:
            return False
        
        image_type = getattr(general_image, 'image_type', [])
        return 'PORTAL' in image_type or 'VERIFICATION' in image_type
    
    @property
    def is_multi_frame(self) -> bool:
        """Check if this is a multi-frame RT image.
        
        Returns:
            True if multi-frame module is present
        """
        return 'multi_frame' in self._modules
    
    @property
    def has_cine_capability(self) -> bool:
        """Check if this image supports cine playback.
        
        Returns:
            True if cine module is present
        """
        return 'cine' in self._modules
    
    def get_rt_imaging_parameters(self) -> Dict[str, Any]:
        """Get RT imaging parameters summary.
        
        Returns:
            Dictionary with key RT imaging parameters
        """
        rt_image_module = self._modules.get('rt_image')
        if not rt_image_module:
            return {}
        
        return {
            'image_label': getattr(rt_image_module, 'rt_image_label', None),
            'image_plane': getattr(rt_image_module, 'rt_image_plane', None),
            'rt_image_sid': getattr(rt_image_module, 'rt_image_sid', None),
            'radiation_machine_name': getattr(rt_image_module, 'radiation_machine_name', None),
            'primary_dosimeter_unit': getattr(rt_image_module, 'primary_dosimeter_unit', None),
            'gantry_angle': getattr(rt_image_module, 'gantry_angle', None),
            'beam_limiting_device_angle': getattr(rt_image_module, 'beam_limiting_device_angle', None),
            'patient_support_angle': getattr(rt_image_module, 'patient_support_angle', None),
            'image_plane_pixel_spacing': getattr(rt_image_module, 'image_plane_pixel_spacing', None)
        }
```

### RT Structure Set IOD

```python
class RTStructureSetIOD(BaseIOD):
    """RT Structure Set Information Object Definition.
    
    Combines radiotherapy structure modules into a complete DICOM RT Structure Set object
    containing ROI definitions, contour geometry, and structure classifications.
    """
    
    def __init__(self,
                 # Required modules for all RT Structure Sets
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 structure_set_module: StructureSetModule,
                 roi_contour_module: ROIContourModule,
                 rt_roi_observations_module: RTROIObservationsModule,
                 # Optional modules for enhanced structure information
                 approval_module: Optional[ApprovalModule] = None,
                 # RT-specific optional modules
                 rt_physician_approval_module: Optional[RTPhysicianApprovalModule] = None):
        """Create RT Structure Set IOD from constituent modules.
        
        Args:
            patient_module: Patient demographic and identification information
            general_study_module: Study-level metadata including study UID and dates
            rt_series_module: RT series metadata (must have modality='RTSTRUCT')
            frame_of_reference_module: Spatial coordinate system reference matching planning images
            general_equipment_module: Structure definition system equipment information  
            structure_set_module: Structure set metadata, referenced images, and ROI definitions
            roi_contour_module: 3D contour geometry data for all defined structures
            rt_roi_observations_module: Clinical structure classifications and properties
            
            approval_module: Optional general approval information for structure set
            rt_physician_approval_module: Optional physician-specific approval and sign-off
            
        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect
            
        Example:
            ```python
            # Create RT Structure Set IOD with target and organ contours
            rt_struct = RTStructureSetIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John", 
                    patient_id="12345"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTSTRUCT",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"  # Must match planning CT
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Eclipse Treatment Planning System"
                ),
                structure_set_module=StructureSetModule.from_required_elements(
                    structure_set_label="Planning Structures",
                    referenced_frame_of_reference_sequence=ref_frame_seq
                ),
                roi_contour_module=ROIContourModule.from_required_elements(
                    roi_contour_sequence=[
                        ROIContourModule.create_roi_contour_item(
                            referenced_roi_number=1,
                            contour_sequence=ptv_contours,
                            roi_display_color=[255, 0, 0]  # Red for PTV
                        ),
                        ROIContourModule.create_roi_contour_item(
                            referenced_roi_number=2, 
                            contour_sequence=heart_contours,
                            roi_display_color=[0, 255, 0]  # Green for heart
                        )
                    ]
                ),
                rt_roi_observations_module=RTROIObservationsModule.from_required_elements(
                    rt_roi_observations_sequence=[
                        RTROIObservationsModule.create_roi_observation_item(
                            observation_number=1,
                            referenced_roi_number=1,
                            roi_observation_label="PTV",
                            rt_roi_interpreted_type="PTV"
                        ),
                        RTROIObservationsModule.create_roi_observation_item(
                            observation_number=2,
                            referenced_roi_number=2, 
                            roi_observation_label="Heart",
                            rt_roi_interpreted_type="ORGAN"
                        )
                    ]
                )
            )
            
            # Access structure information
            structures = rt_struct.get_structure_summary()
            print(f"Number of structures: {len(structures['roi_names'])}")
            ```
        """
        super().__init__()
        
        # Validate RT Structure Set-specific requirements
        if rt_series_module.modality != 'RTSTRUCT':
            raise IODValidationError("rt_series_module must have modality='RTSTRUCT'")
        
        # Validate that required structure modules are consistent
        self._validate_dependencies(structure_set_module, roi_contour_module, rt_roi_observations_module)
        
        # Store all modules as live references
        self._modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'structure_set': structure_set_module,
            'roi_contour': roi_contour_module,
            'rt_roi_observations': rt_roi_observations_module
        }
        
        # Add optional modules if provided
        optional_modules = {
            'approval': approval_module,
            'rt_physician_approval': rt_physician_approval_module
        }
        
        for name, module in optional_modules.items():
            if module is not None:
                self._modules[name] = module
        
    
    def _validate_dependencies(self, structure_set_module, roi_contour_module, rt_roi_observations_module) -> None:
        """Validate RT Structure Set module consistency."""
        # Additional validation could check that ROI numbers are consistent
        # between structure_set, roi_contour, and rt_roi_observations modules
        pass
    
    @property
    def has_approval_data(self) -> bool:
        """Check if structure set has approval information.
        
        Returns:
            True if approval module is present
        """
        return 'approval' in self._modules or 'rt_physician_approval' in self._modules
    
    @property
    def is_physician_approved(self) -> bool:
        """Check if structure set has physician approval.
        
        Returns:
            True if RT physician approval module is present
        """
        return 'rt_physician_approval' in self._modules
    
    def get_structure_summary(self) -> Dict[str, Any]:
        """Get structure set summary information.
        
        Returns:
            Dictionary with structure names, types, and counts
        """
        structure_module = self._modules.get('structure_set')
        roi_contour_module = self._modules.get('roi_contour')
        observations_module = self._modules.get('rt_roi_observations')
        
        summary = {
            'structure_set_label': getattr(structure_module, 'structure_set_label', None) if structure_module else None,
            'roi_names': [],
            'roi_types': [],
            'roi_colors': [],
            'number_of_rois': 0
        }
        
        if observations_module:
            roi_obs_seq = getattr(observations_module, 'rt_roi_observations_sequence', [])
            for obs in roi_obs_seq:
                summary['roi_names'].append(getattr(obs, 'roi_observation_label', 'Unknown'))
                summary['roi_types'].append(getattr(obs, 'rt_roi_interpreted_type', 'Unknown'))
            summary['number_of_rois'] = len(roi_obs_seq)
        
        if roi_contour_module:
            roi_contour_seq = getattr(roi_contour_module, 'roi_contour_sequence', [])
            for contour in roi_contour_seq:
                color = getattr(contour, 'roi_display_color', [128, 128, 128])
                summary['roi_colors'].append(color)
        
        return summary
```

## Usage Patterns and Best Practices

### Memory Management Considerations

```python
# EFFICIENT: Reuse modules across multiple IODs
patient_mod = PatientModule.from_required_elements(patient_name="Doe^John", patient_id="12345")
study_mod = GeneralStudyModule.from_required_elements(study_instance_uid="*******.5")
frame_mod = FrameOfReferenceModule.from_required_elements(frame_of_reference_uid="*******.7")

# Large dose array (e.g., 512x512x200 = ~200MB)
large_dose_array = np.random.random((512, 512, 200)).astype(np.float32)
dose_mod = RTDoseModule.from_required_elements(
    dose_units="GY",
    dose_type="PHYSICAL",
    dose_grid_scaling=0.001,
    pixel_data=large_dose_array  # This array is shared by reference
)

# Multiple IODs share the same large array - memory efficient!
rt_dose_1 = RTDoseIOD(patient_mod, study_mod, ..., dose_mod)  # ~200MB
rt_dose_2 = RTDoseIOD(patient_mod, study_mod, ..., dose_mod)  # +small overhead, not +200MB
rt_dose_3 = RTDoseIOD(patient_mod, study_mod, ..., dose_mod)  # +small overhead, not +200MB

# Total memory usage: ~200MB + small metadata overhead (not 600MB!)
```

### Explicit Dataset Generation Pattern

```python
# Clear and explicit workflow for dataset generation
rt_dose = RTDoseIOD(...)

# Generate dataset for initial export
initial_dataset = rt_dose.generate_dataset()
initial_dataset.save_as("dose_v1.dcm")

# Modify module data
dose_module = rt_dose.get_module('rt_dose')
dose_module.dose_grid_scaling = 0.002

# Generate fresh dataset reflecting changes
updated_dataset = rt_dose.generate_dataset()
updated_dataset.save_as("dose_v2.dcm")

# Generate file dataset with proper DICOM headers
file_dataset = rt_dose.generate_file_dataset()
file_dataset.save_as("dose_with_headers.dcm")
```

### Validation and Error Handling

```python
try:
    # This will fail due to missing required beam module for curative plan
    rt_plan = RTPlanIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        rt_series_module=rt_series_mod,
        frame_of_reference_module=frame_mod,
        general_equipment_module=equipment_mod,
        rt_general_plan_module=RTGeneralPlanModule.from_required_elements(
            rt_plan_intent="CURATIVE"  # Requires beam module
        )
        # Missing rt_beams_module!
    )
except IODValidationError as e:
    print(f"IOD validation failed: {e}")
    # Handle error appropriately
```

### Common Workflow Patterns

```python
# Pattern 1: Sequential IOD creation for complete treatment workflow
def create_treatment_workflow(patient_data, ct_data, plan_data, dose_data, struct_data):
    """Create complete RT workflow IODs."""
    
    # Shared modules
    patient_mod = PatientModule.from_required_elements(**patient_data)
    study_mod = GeneralStudyModule.from_required_elements(study_instance_uid=generate_uid())
    frame_mod = FrameOfReferenceModule.from_required_elements(frame_of_reference_uid=generate_uid())
    equipment_mod = GeneralEquipmentModule.from_required_elements(manufacturer="TPS v1.0")
    
    # CT IOD
    ct_iod = CTIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        general_series_module=GeneralSeriesModule.from_required_elements(modality="CT", ...),
        frame_of_reference_module=frame_mod,
        general_equipment_module=equipment_mod,
        **ct_data
    )
    
    # RT Structure Set IOD (references CT)
    struct_iod = RTStructureSetIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        rt_series_module=RTSeriesModule.from_required_elements(modality="RTSTRUCT", ...),
        frame_of_reference_module=frame_mod,  # Same frame as CT
        general_equipment_module=equipment_mod,
        **struct_data
    )
    
    # RT Plan IOD (references structures)
    plan_iod = RTPlanIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        rt_series_module=RTSeriesModule.from_required_elements(modality="RTPLAN", ...),
        frame_of_reference_module=frame_mod,  # Same frame as CT and structures
        general_equipment_module=equipment_mod,
        **plan_data
    )
    
    # RT Dose IOD (calculated from plan)
    dose_iod = RTDoseIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        rt_series_module=RTSeriesModule.from_required_elements(modality="RTDOSE", ...),
        frame_of_reference_module=frame_mod,  # Same frame as everything else
        general_equipment_module=equipment_mod,
        **dose_data
    )
    
    return {
        'ct': ct_iod,
        'structures': struct_iod,
        'plan': plan_iod,
        'dose': dose_iod
    }

# Pattern 2: Export to DICOM files
def export_workflow_to_dicom(workflow_iods, output_dir):
    """Export all workflow IODs to DICOM files."""
    for name, iod in workflow_iods.items():
        filename = f"{name}_{iod.get_module('patient').patient_id}.dcm"
        filepath = os.path.join(output_dir, filename)
        iod.generate_file_dataset().save_as(filepath)
        print(f"Exported {name} to {filepath}")

# Pattern 3: Multi-Machine Plan Generation with Explicit Dataset Creation
def create_multi_machine_plans(base_plan_data, machine_configs):
    """Create RT Plan IODs for multiple machines with explicit dataset generation.
    
    This pattern demonstrates efficient dataset generation: create one RT Plan IOD,
    then modify modules and generate fresh datasets for different machines.
    Each generate_dataset() call creates a new dataset reflecting current module state.
    """
    
    # Create shared modules that don't change between machines
    patient_mod = PatientModule.from_required_elements(**base_plan_data['patient'])
    study_mod = GeneralStudyModule.from_required_elements(study_instance_uid=generate_uid())
    rt_series_mod = RTSeriesModule.from_required_elements(
        modality="RTPLAN",
        series_instance_uid=generate_uid(),
        series_description="Multi-Machine Treatment Plan"
    )
    frame_mod = FrameOfReferenceModule.from_required_elements(
        frame_of_reference_uid=generate_uid()
    )
    rt_plan_mod = RTGeneralPlanModule.from_required_elements(**base_plan_data['plan'])
    rt_beams_mod = RTBeamsModule.from_required_elements(**base_plan_data['beams'])
    
    # Create base equipment module (will be modified for each machine)
    base_equipment_mod = GeneralEquipmentModule.from_required_elements(
        manufacturer="Varian Medical Systems",
        station_name="TrueBeam STx"  # Will be updated for each machine
    )
    
    # Create single RT Plan IOD with base equipment
    rt_plan_iod = RTPlanIOD(
        patient_module=patient_mod,
        general_study_module=study_mod,
        rt_series_module=rt_series_mod,
        frame_of_reference_module=frame_mod,
        general_equipment_module=base_equipment_mod,
        rt_general_plan_module=rt_plan_mod,
        rt_beams_module=rt_beams_mod
    )
    
    machine_datasets = {}
    
    # Generate datasets for each machine configuration
    for machine_name, machine_config in machine_configs.items():
        print(f"Generating plan for {machine_name}...")
        
        # Modify equipment module for this machine
        equipment_module = rt_plan_iod.get_module('general_equipment')
        equipment_module.station_name = machine_config['station_name']
        equipment_module.device_serial_number = machine_config['serial_number']
        equipment_module.software_versions = machine_config['software_version']
        
        # Update beam machine names in the beams module
        beams_module = rt_plan_iod.get_module('rt_beams')
        for beam in beams_module.beam_sequence:
            beam.treatment_machine_name = machine_config['station_name']
            beam.primary_dosimeter_unit = machine_config.get('dosimeter_unit', 'MU')
        
        # Generate fresh dataset with updated machine configuration
        # Each call creates a new dataset reflecting current module state
        machine_dataset = rt_plan_iod.generate_dataset()
        
        # Store dataset with machine-specific filename
        machine_datasets[machine_name] = {
            'dataset': machine_dataset,
            'filename': f"rtplan_{machine_name}_{patient_mod.patient_id}.dcm"
        }
        
        print(f"✓ Dataset generated for {machine_name} "
              f"(Station: {machine_config['station_name']}, "
              f"S/N: {machine_config['serial_number']})")
    
    return machine_datasets

# Example usage of multi-machine pattern
def example_multi_machine_workflow():
    """Example showing explicit dataset generation benefits for multi-machine planning."""
    
    # Base plan data (same for all machines)
    base_plan = {
        'patient': {
            'patient_name': 'Doe^John',
            'patient_id': '12345',
            'patient_birth_date': '19700101'
        },
        'plan': {
            'rt_plan_label': 'VMAT Prostate',
            'rt_plan_date': '20240101',
            'rt_plan_intent': 'CURATIVE'
        },
        'beams': {
            'beam_sequence': [
                # Simplified beam data - would contain full beam definitions
                {'beam_number': 1, 'beam_name': 'Arc 1', 'gantry_angle': 179.0},
                {'beam_number': 2, 'beam_name': 'Arc 2', 'gantry_angle': 181.0}
            ]
        }
    }
    
    # Different machine configurations
    machine_configs = {
        'truebeam_stx_1': {
            'station_name': 'TrueBeam STx #1',
            'serial_number': 'TB001',
            'software_version': 'v2.7.1'
        },
        'truebeam_stx_2': {
            'station_name': 'TrueBeam STx #2', 
            'serial_number': 'TB002',
            'software_version': 'v2.7.1'
        },
        'clinac_ix': {
            'station_name': 'Clinac iX',
            'serial_number': 'CX001',
            'software_version': 'v1.4.2'
        },
        'edge': {
            'station_name': 'Edge',
            'serial_number': 'ED001',
            'software_version': 'v1.1.0'
        }
    }
    
    # Generate datasets for all machines
    machine_datasets = create_multi_machine_plans(base_plan, machine_configs)
    
    # Export all machine-specific plans
    output_dir = "multi_machine_plans"
    os.makedirs(output_dir, exist_ok=True)
    
    for machine_name, data in machine_datasets.items():
        filepath = os.path.join(output_dir, data['filename'])
        data['dataset'].save_as(filepath)
        print(f"Exported {machine_name} plan to {filepath}")
    
    print(f"\n✓ Generated {len(machine_datasets)} machine-specific RT Plan datasets")
    print(f"✓ Memory efficient: Single IOD + module modifications + explicit generation")
    print(f"✓ Total datasets created with minimal overhead")
    
    return machine_datasets
```

## Performance and Memory Considerations

### Large Data Handling
- **Pixel Data Sharing**: Large arrays (CT images, dose grids) are shared by reference between modules and IODs
- **Explicit Dataset Generation**: Complete DICOM datasets generated on-demand via `generate_dataset()` calls
- **No Caching Overhead**: Fresh datasets created each time, eliminating cache management complexity

### Memory Usage Estimates
- **CT IOD**: ~500MB for 512x512x200 slices
- **RT Dose IOD**: ~200MB for 128x128x100 dose grid  
- **RT Structure Set IOD**: ~1-50MB depending on contour complexity
- **RT Plan IOD**: ~1-10MB for beam definitions
- **Multiple IODs from same modules**: Minimal additional memory overhead due to reference sharing

### Best Practices
1. **Reuse modules** across multiple IODs when possible
2. **Generate datasets explicitly** when ready to export or analyze data
3. **Use consistent Frame of Reference UIDs** across related IODs
4. **Validate early** - IOD constructors catch dependency errors immediately
5. **Choose appropriate generation method** - Use `generate_dataset()` for data manipulation, `generate_file_dataset()` for file I/O

This architecture provides maximum ease of use through explicit typing and comprehensive docstrings while maintaining memory efficiency through intelligent data sharing and explicit dataset generation.

## Implementation Priority Order

### Phase 1: Proof of Concept Validation (Priority 1)
**Target IOD**: RT Dose IOD
- **Rationale**: Simplest IOD to validate the BaseIOD architecture and lazy dataset generation
- **Key Validation Points**: Module composition, dataset caching, memory efficiency with large arrays
- **Success Criteria**: Working RT Dose IOD with module integration and DICOM file export

### Phase 2: Imaging Foundation (Priority 2)
**Target IODs**: CT IOD → RT Image IOD
- **CT IOD**: Standard medical imaging foundation for treatment planning workflows
- **RT Image IOD**: Portal imaging and verification workflows, builds on CT patterns
- **Key Validation Points**: Image pixel data handling, spatial coordinate consistency, multi-frame support

### Phase 3: Planning and Structure Definition (Priority 3)
**Target IODs**: RT Structure Set IOD → RT Plan IOD
- **RT Structure Set IOD**: ROI definitions and contour geometry, moderate complexity
- **RT Plan IOD**: Treatment planning with beam definitions, highest complexity IOD
- **Key Validation Points**: Cross-referencing between IODs, conditional module validation, treatment workflow integration

### Phase 4: Advanced Imaging (Priority 4)
**Target IOD**: MR IOD
- **MR IOD**: Advanced imaging for treatment planning and image guidance
- **Key Validation Points**: Enhanced imaging features, sequence-specific modules, multi-modal workflows

### Implementation Dependencies
Each IOD requires its constituent modules to be implemented first:
- **All IODs**: PatientModule, GeneralStudyModule, GeneralEquipmentModule
- **Imaging IODs**: GeneralImageModule, ImagePixelModule, ImagePlaneModule
- **RT IODs**: RTSeriesModule, FrameOfReferenceModule
- **Specific Modules**: CTImageModule, MRImageModule, RTImageModule, RTDoseModule, etc.

Build order follows dependency chain: Core modules → Imaging modules → RT-specific modules → IOD implementations.

## Success Criteria and Architecture Validation

### Core Architecture Requirements

#### 1. Explicit Constructor Pattern
- **Complete Type Specification**: All required, optional, and conditional modules explicitly typed in constructor signatures
- **IDE IntelliSense Support**: Full parameter autocomplete and type checking for all IOD constructors
- **Clear Documentation**: Comprehensive docstrings with usage examples for every IOD constructor
- **Validation at Construction**: Runtime validation of module dependencies and compatibility during IOD instantiation

#### 2. Explicit Dataset Generation with Live References
- **Memory Efficiency**: Module data shared by reference, not copied, between multiple IODs
- **Live References**: Changes to module data reflected in subsequent dataset generation calls
- **Explicit Generation**: DICOM datasets generated on-demand via `generate_dataset()` method calls
- **No Cache Management**: Fresh datasets created each time, eliminating cache invalidation complexity

#### 3. BaseIOD Independence
- **No pydicom Inheritance**: BaseIOD does not inherit from pydicom classes for clean separation
- **On-Demand File Generation**: IODs can generate valid DICOM files on-demand without constant pydicom state
- **Module Composition**: IODs built through module composition, not inheritance hierarchies
- **Resource Management**: Proper management of large data arrays and memory usage

### Functional Requirements

#### 4. DICOM Compliance and Validation
- **Standards Conformance**: Generated DICOM files pass external validation tools (DVTK, dcm4che)
- **Module Dependency Validation**: Conditional module requirements enforced at runtime
- **Cross-Reference Integrity**: UID consistency maintained across related IODs in workflows
- **Data Type Compliance**: All DICOM data elements conform to VR and value constraints

#### 5. Real-World Workflow Support
- **Treatment Planning Integration**: Complete RT workflows (CT → Structure → Plan → Dose → Image)
- **Large Data Handling**: Efficient management of 3D arrays (CT volumes, dose grids) up to 500MB
- **Multi-IOD Workflows**: Module reuse across multiple IODs with consistent UIDs and references
- **File Export**: Reliable save/load cycles preserving all data integrity

### User Experience Goals

#### 6. Developer Productivity
- **Explicit Guidance**: Constructor signatures immediately reveal all available modules and requirements
- **Error Prevention**: Invalid IOD configurations caught at construction time, not runtime
- **Logical Properties**: Clear boolean properties help developers understand IOD capabilities
- **Rich Metadata Access**: Summary methods provide easy access to key IOD parameters

#### 7. Performance and Scalability
- **Memory Optimization**: Large pixel data arrays shared efficiently across IODs
- **Lazy Evaluation**: Computational overhead only when DICOM datasets actually needed
- **Batch Operations**: Multiple IODs can be created and managed with minimal memory overhead
- **Resource Monitoring**: Clear patterns for managing memory usage in large workflows

### Technical Validation Criteria

#### 8. Code Quality and Maintainability
- **Type Safety**: Complete type annotations for all methods and properties
- **Consistent Patterns**: Uniform constructor patterns across all IOD implementations
- **Comprehensive Testing**: Unit tests covering all IOD constructors, validation, and edge cases
- **Documentation Coverage**: Every public method and property documented with examples

#### 9. Integration and Extensibility
- **Module System Integration**: Seamless integration with existing module architecture
- **Extension Points**: Clear patterns for adding new IODs following established conventions
- **Backward Compatibility**: Changes don't break existing module implementations
- **Future-Proofing**: Architecture supports additional DICOM IODs and emerging RT techniques

## Testing Standards and Validation Framework

### Unit Testing Requirements

#### 1. Constructor Validation Testing
- **Valid Construction**: Test successful IOD creation with all required modules
- **Module Type Validation**: Verify type checking for incorrect module types in constructors
- **Conditional Module Dependencies**: Test validation of conditional module requirements (e.g., cine requires multi-frame)
- **Modality Validation**: Verify IODs reject modules with incorrect modalities (e.g., CT IOD with MR series)
- **Error Message Quality**: Ensure IODValidationError messages are clear and actionable

#### 2. Explicit Dataset Generation Testing
- **Fresh Generation**: Verify each `generate_dataset()` call creates a new dataset reflecting current module state
- **Live Reference Updates**: Test that module changes are reflected in subsequent dataset generation calls
- **Memory Efficiency**: Validate that large arrays are shared by reference, not copied
- **Dataset Completeness**: Ensure generated datasets contain all module data correctly merged
- **Performance**: Measure dataset generation time for large IODs (target <100ms for typical cases)

#### 3. Module Integration Testing
- **Live Reference Behavior**: Test that changes to modules are reflected in subsequent dataset generation calls
- **Module Retrieval**: Verify `get_module()` returns correct live references to constituent modules
- **Module Modification**: Test external module changes are reflected in new dataset generation calls
- **Cross-IOD Sharing**: Validate modules can be safely shared across multiple IOD instances
- **Memory Management**: Test that module sharing doesn't cause memory leaks or reference cycles

#### 4. Logical Properties Testing
- **Boolean Property Accuracy**: Test all `is_*` and `has_*` properties return correct values
- **Conditional Module Detection**: Verify properties correctly identify presence of optional/conditional modules
- **Edge Cases**: Test properties with empty modules, missing data, and malformed modules
- **Performance**: Ensure property access is fast (<1ms) for all logical property queries

### Integration Testing Requirements

#### 5. DICOM Compliance Testing
- **File Generation**: Test IODs can generate valid DICOM files that pass external validation
- **Standards Conformance**: Validate generated files using DVTK, dcm4che, or similar tools
- **Data Integrity**: Verify all module data correctly appears in generated DICOM datasets
- **UID Consistency**: Test that UIDs are properly generated and consistent across related IODs
- **File Size Limits**: Ensure large IODs (>100MB) can be successfully written and read

#### 6. Workflow Integration Testing
- **Multi-IOD Workflows**: Test complete RT workflows (CT → Structure → Plan → Dose → Image)
- **Cross-Reference Validation**: Verify UIDs and references are consistent across workflow IODs
- **Module Reuse**: Test that shared modules work correctly across multiple IODs in workflows
- **Frame of Reference Consistency**: Validate spatial coordinate consistency across imaging IODs
- **Save/Load Cycles**: Test that IOD data survives complete save-to-file and load-from-file cycles

#### 7. Performance and Memory Testing
- **Large Data Handling**: Test IODs with large pixel arrays (512×512×200 CT, 3D dose grids)
- **Memory Usage Monitoring**: Measure actual memory consumption vs. expected for shared modules
- **Batch Operations**: Test creating multiple IODs efficiently with shared modules
- **Memory Leak Detection**: Verify no memory leaks during IOD creation/destruction cycles
- **Concurrent Access**: Test thread-safety of IOD operations and module sharing

### Specialized Testing by IOD Type

#### 8. CT IOD Testing
- **Multi-slice Handling**: Test CT IODs with multiple image slices and proper spatial ordering
- **Contrast Detection**: Verify `has_contrast` property accuracy based on contrast bolus module
- **Enhanced CT Support**: Test multi-frame module integration for enhanced CT sequences
- **Pixel Data Integrity**: Validate CT pixel arrays are correctly stored and retrieved

#### 9. RT-Specific IOD Testing
- **Portal Image Verification**: Test RT Image IODs with portal imaging workflows
- **Dose Distribution Accuracy**: Verify 3D dose arrays are correctly scaled and positioned
- **Treatment Plan Validation**: Test RT Plan IODs with different treatment techniques (EBRT, brachy, ion)
- **Structure Set Consistency**: Validate ROI contours and observations are properly linked
- **Cross-IOD References**: Test reference integrity between RT plans, doses, and structure sets

#### 10. Edge Case and Error Handling Testing
- **Invalid Module Combinations**: Test IODs gracefully handle incompatible module combinations
- **Missing Required Data**: Verify appropriate errors when required module data is missing
- **Malformed Input Data**: Test robustness against corrupted or invalid module data
- **Resource Exhaustion**: Test behavior when memory or disk space is limited
- **Concurrent Modification**: Test handling of modules modified during IOD operations

### Test Infrastructure Requirements

#### 11. Test Data Management
- **Representative Datasets**: Create test datasets covering all IOD types and usage patterns
- **Large Data Simulation**: Generate realistic large datasets for performance testing
- **Edge Case Data**: Create test data for boundary conditions and error scenarios
- **Real-World Examples**: Include actual clinical data samples (appropriately anonymized)
- **Synthetic Data Generation**: Tools for creating parameterized test datasets

#### 12. Automated Testing Pipeline
- **Continuous Integration**: All tests run automatically on code changes
- **Performance Regression Detection**: Monitor performance metrics across test runs
- **Memory Usage Tracking**: Automated detection of memory usage increases
- **DICOM Validation Integration**: Automated external DICOM validation in test pipeline
- **Coverage Reporting**: Maintain >90% code coverage for all IOD implementations

### Test Documentation and Reporting

#### 13. Test Coverage Requirements
- **Constructor Coverage**: Every IOD constructor parameter combination tested
- **Property Coverage**: All logical properties tested with various module configurations
- **Method Coverage**: All public methods tested with valid and invalid inputs
- **Error Path Coverage**: All error conditions and exception paths tested
- **Integration Scenario Coverage**: All documented workflow patterns tested

#### 14. Performance Benchmarks
- **Dataset Generation Time**: Target <100ms for typical IOD dataset generation
- **Memory Efficiency**: Shared modules should not duplicate large data arrays
- **File I/O Performance**: DICOM file save/load should complete within reasonable time limits
- **Scalability Metrics**: Performance characteristics documented for various data sizes
- **Regression Testing**: Performance regression alerts when metrics degrade beyond thresholds
