from collections.abc import Mapping
from typing import Any, Final

from .__version__ import version

##

f2py_version: Final = version

cb_routine_rules: Final[dict[str, str | list[str]]] = ...
cb_rout_rules: Final[list[dict[str, str | Any]]] = ...
cb_arg_rules: Final[list[dict[str, str | Any]]] = ...

cb_map: Final[dict[str, list[list[str]]]] = ...

def buildcallbacks(m: Mapping[str, object]) -> None: ...
def buildcallback(rout: Mapping[str, object], um: Mapping[str, object]) -> None: ...
